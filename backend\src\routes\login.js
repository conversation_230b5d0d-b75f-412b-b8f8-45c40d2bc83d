import express from 'express';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import userService from '../services/userService.js';
import { detectSuspiciousLogin } from '../middleware/suspiciousLogin.js';

const router = express.Router();

// Middleware para tratar erros de validação
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
      })),
    });
  }
  next();
};

// Lista de senhas comuns (em produção, seria um arquivo separado)
const senhasComuns = [
  '123456',
  'password',
  '123456789',
  '12345678',
  '12345',
  'qwerty',
  'abc123',
  'football',
  '1234567',
  'monkey',
  '111111',
  'letmein',
  '1234',
  '1234567890',
  'dragon',
  'baseball',
  'sunshine',
  'iloveyou',
  'trustno1',
  'princess',
  'admin',
  'welcome',
  'solo',
  'master',
  'hello',
  'freedom',
  'whatever',
  'qazwsx',
  'ninja',
  'michael',
];

// Validações para login
const validarLogin = [
  body('email')
    .isEmail()
    .withMessage('Email deve ter formato válido')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email muito longo'),

  body('password')
    .notEmpty()
    .withMessage('Senha é obrigatória')
    .isLength({ max: 128 })
    .withMessage('Senha muito longa'),

  handleValidationErrors,
];

// Rota de teste
router.get('/test', (req, res) => {
  res.json({ message: 'Rota de login funcionando!' });
});

// Rota de login com detecção de login suspeito
router.post('/', async (req, res) => {
  try {
    console.log('🔍 Rota de login chamada');
    console.log('🔍 Body:', req.body);
    
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email e senha são obrigatórios' });
    }
    
    console.log('🔍 Tentativa de login:', { email, password: '***' });

    // Verificar credenciais usando o serviço
    const result = await userService.verifyCredentials(email, password);
    
    console.log('🔍 Resultado da verificação:', result);

    if (!result.success) {
      return res.status(401).json({ error: result.error });
    }

    const user = result.user;
    
    console.log('🔍 Usuário encontrado:', user.id);

    // Gerar token JWT
    const accessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        name: user.name,
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' } // Access token expira em 15 minutos
    );

    // Gerar refresh token
    const refreshToken = jwt.sign(
      {
        userId: user.id,
        type: 'refresh', // Identifica que é um refresh token
      },
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, // Usa secret diferente se disponível
      { expiresIn: '7d' } // Refresh token expira em 7 dias
    );
    
    console.log(`✅ Login realizado com sucesso: ${user.email}`);

    // Retornar tokens e dados do usuário
    res.json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        githubId: user.githubId,
        githubUsername: user.githubUsername,
        emailVerified: user.emailVerified,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        preferences: user.preferences,
      },
    });

  } catch (error) {
    console.error('❌ Erro no login:', error.message);
    console.error('❌ Stack trace:', error.stack);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

export default router; 