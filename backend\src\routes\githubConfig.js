import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from '../middleware/auth.js';
// import { Octokit } from 'octokit'; // 🎭 REMOVIDO - USANDO DADOS FICTÍCIOS

const router = express.Router();
import {
  storeGitHubToken,
  validateGitHubToken,
  getRepositories,
  getCommits,
  getRepository,
} from '../services/githubService.js';

// Middleware para tratar erros de validação
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
      })),
    });
  }
  next();
};

// Validações para configuração do token GitHub
const validarGitHubToken = [
  body('githubToken')
    .notEmpty()
    .withMessage('Token GitHub é obrigatório')
    .isLength({ min: 40 })
    .withMessage('Token GitHub deve ter pelo menos 40 caracteres')
    .matches(/^ghp_[a-zA-Z0-9]{36}$|^github_pat_[a-zA-Z0-9]{82}$/)
    .withMessage('Token GitHub deve ter formato válido (ghp_ ou github_pat_)'),

  handleValidationErrors,
];

/**
 * @route POST /api/github/config
 * @desc Configura o token GitHub do usuário
 * @access Private
 */
router.post(
  '/config',
  authenticateToken,
  validarGitHubToken,
  async (req, res) => {
    try {
      const { githubToken } = req.body;
      const userId = req.user.userId;

      // Validar se o token é válido fazendo uma chamada para a API GitHub
      const isValid = await validateGitHubToken(githubToken);

      if (!isValid) {
        return res.status(400).json({
          error: 'Token GitHub inválido',
          error_en: 'Invalid GitHub token',
        });
      }

      // Armazenar o token
      storeGitHubToken(userId, githubToken);

      res.json({
        success: true,
        message: 'Token GitHub configurado com sucesso',
        message_en: 'GitHub token configured successfully',
      });
    } catch (error) {
      console.error('Erro ao configurar token GitHub:', error);
      res.status(500).json({
        error: 'Erro interno do servidor ao configurar token',
        error_en: 'Internal server error while configuring token',
      });
    }
  }
);

/**
 * @route GET /api/github/config/status
 * @desc Verifica se o usuário tem token GitHub configurado
 * @access Private
 */
router.get('/config/status', authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;
    const { getGitHubToken } = require('../services/githubService');

    const token = getGitHubToken(userId);
    const hasToken = !!token;

    res.json({
      success: true,
      data: {
        hasGitHubToken: hasToken,
        isConfigured: hasToken,
      },
      message: hasToken
        ? 'Token GitHub configurado'
        : 'Token GitHub não configurado',
      message_en: hasToken
        ? 'GitHub token configured'
        : 'GitHub token not configured',
    });
  } catch (error) {
    console.error('Erro ao verificar status do token GitHub:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      error_en: 'Internal server error',
    });
  }
});

/**
 * @route GET /api/github/repositories
 * @desc Busca repositórios do usuário autenticado
 * @access Private
 */
router.get('/repositories', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const githubToken = req.headers['x-github-token'];

    // Verificar se o token GitHub foi fornecido
    if (!githubToken) {
      return res.status(401).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Use o header x-github-token para fornecer o token GitHub'
      });
    }

    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log('🎭 Retornando repositórios fictícios...');

    // 🎭 REPOSITÓRIOS FICTÍCIOS
    const repos = [
      {
        id: 1,
        name: 'code2post-frontend',
        full_name: 'gabrielcamarate/code2post-frontend',
        description: 'Frontend moderno em React + TypeScript para automação de posts do LinkedIn',
        private: false,
        html_url: 'https://github.com/gabrielcamarate/code2post-frontend',
        updated_at: '2024-01-30T10:30:00Z',
        language: 'TypeScript',
        stargazers_count: 42,
        forks_count: 8,
        owner: {
          login: 'gabrielcamarate',
          avatar_url: 'https://github.com/gabrielcamarate.png'
        }
      },
      {
        id: 2,
        name: 'ai-content-generator',
        full_name: 'gabrielcamarate/ai-content-generator',
        description: 'Sistema de geração de conteúdo usando IA para redes sociais',
        private: false,
        html_url: 'https://github.com/gabrielcamarate/ai-content-generator',
        updated_at: '2024-01-29T15:45:00Z',
        language: 'Python',
        stargazers_count: 89,
        forks_count: 23,
        owner: {
          login: 'gabrielcamarate',
          avatar_url: 'https://github.com/gabrielcamarate.png'
        }
      }
    ];

    // Formatar dados dos repositórios
    const formattedRepos = repos.map(repo => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name,
      description: repo.description,
      private: repo.private,
      fork: repo.fork,
      language: repo.language,
      stargazers_count: repo.stargazers_count,
      forks_count: repo.forks_count,
      updated_at: repo.updated_at,
      html_url: repo.html_url,
      clone_url: repo.clone_url,
    }));

    res.json({
      success: true,
      repositories: formattedRepos,
      total: formattedRepos.length,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar repositórios:', error.message);

    // Se o erro for de autenticação GitHub
    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar repositórios',
      error_en: 'Error fetching repositories',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:repoName/commits
 * @desc Busca commits de um repositório específico
 * @access Private
 */
router.get('/repositories/:repoName/commits', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { repoName } = req.params;
    const { limit = 10 } = req.query;
    const commits = await getCommits(userId, repoName, 'main', parseInt(limit));

    res.json(commits);
  } catch (error) {
    console.error('❌ Erro ao buscar commits:', error.message);

    if (error.message === 'Token GitHub não encontrado') {
      return res.status(401).json({
        error: 'Token GitHub não configurado',
        error_en: 'GitHub token not configured',
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar commits',
      error_en: 'Error fetching commits',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/languages
 * @desc Busca linguagens de um repositório específico com percentuais
 * @access Private
 */
router.get('/repositories/:owner/:repo/languages', authenticateToken, async (req, res) => {
  // Adicionar headers CORS para esta rota específica
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-github-token');
  res.header('Access-Control-Allow-Credentials', 'true');
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(400).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Cabeçalho x-github-token é obrigatório',
      });
    }

    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log(`🎭 Retornando linguagens fictícias para ${owner}/${repo}...`);

    // 🎭 LINGUAGENS FICTÍCIAS
    const languages = {
      'TypeScript': 125000,
      'CSS': 45000,
      'JavaScript': 22000,
      'HTML': 8000
    };

    // Calcular total de bytes
    const totalBytes = Object.values(languages).reduce((sum, bytes) => sum + bytes, 0);

    // Calcular porcentagens e formatar dados
    const formattedLanguages = Object.entries(languages)
      .map(([language, bytes]) => ({
        name: language,
        bytes,
        percentage: totalBytes > 0 ? ((bytes / totalBytes) * 100).toFixed(1) : '0.0',
      }))
      .sort((a, b) => b.bytes - a.bytes); // Ordenar por bytes (maior primeiro)

    res.json({
      success: true,
      languages: formattedLanguages,
      totalBytes,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar linguagens:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar linguagens do repositório',
      error_en: 'Error fetching repository languages',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/commits-count
 * @desc Busca total de commits de um repositório específico
 * @access Private
 */
router.get('/repositories/:owner/:repo/commits-count', authenticateToken, async (req, res) => {
  // Adicionar headers CORS para esta rota específica
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-github-token');
  res.header('Access-Control-Allow-Credentials', 'true');
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(400).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Cabeçalho x-github-token é obrigatório',
      });
    }

    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log(`🎭 Retornando contagem de commits fictícia para ${owner}/${repo}...`);

    // 🎭 REPOSITÓRIO FICTÍCIO
    const repoInfo = {
      default_branch: 'main'
    };

    // 🎭 CONTAGEM FICTÍCIA DE COMMITS
    let totalCommits = 156; // Número fictício

    res.json({
      success: true,
      repository: `${owner}/${repo}`,
      totalCommits,
      defaultBranch: repoInfo.default_branch,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar total de commits:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar total de commits',
      error_en: 'Error fetching commits count',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/branches
 * @desc Busca branches de um repositório específico
 * @access Private
 */
router.get('/repositories/:owner/:repo/branches', authenticateToken, async (req, res) => {
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(401).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided'
      });
    }

    // Buscar branches do repositório
    const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/branches`, {
      headers: {
        'Authorization': `Bearer ${githubToken}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Code2Post-App'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return res.status(404).json({
          error: 'Repositório não encontrado',
          error_en: 'Repository not found'
        });
      }
      throw new Error(`GitHub API error: ${response.status}`);
    }

    const branches = await response.json();

    res.json({
      success: true,
      branches: branches.map(branch => ({
        name: branch.name,
        commit: {
          sha: branch.commit.sha,
          url: branch.commit.url
        },
        protected: branch.protected
      })),
      count: branches.length
    });

  } catch (error) {
    console.error('❌ Erro ao buscar branches:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar branches',
      error_en: 'Error fetching branches',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/commits
 * @desc Busca commits de um repositório específico com paginação
 * @access Private
 */
router.get('/repositories/:owner/:repo/commits', authenticateToken, async (req, res) => {
  try {
    const { owner, repo } = req.params;
    const { page = 1, per_page = 10 } = req.query;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(401).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided'
      });
    }

    // Buscar commits do repositório com paginação
    const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/commits?page=${page}&per_page=${per_page}`, {
      headers: {
        'Authorization': `Bearer ${githubToken}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Code2Post-App'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return res.status(404).json({
          error: 'Repositório não encontrado',
          error_en: 'Repository not found'
        });
      }
      throw new Error(`GitHub API error: ${response.status}`);
    }

    const commits = await response.json();

    // Buscar total de commits para calcular páginas
    const totalResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}`, {
      headers: {
        'Authorization': `Bearer ${githubToken}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Code2Post-App'
      }
    });

    let totalCommits = 0;
    if (totalResponse.ok) {
      const repoData = await totalResponse.json();
      // Estimativa baseada no tamanho do repositório (GitHub não fornece contagem exata via API simples)
      totalCommits = Math.max(commits.length, parseInt(per_page) * parseInt(page));
    }

    const totalPages = Math.ceil(totalCommits / parseInt(per_page));


    res.json({
      success: true,
      commits: commits.map(commit => ({
        sha: commit.sha,
        commit: {
          message: commit.commit.message,
          author: {
            name: commit.commit.author.name,
            email: commit.commit.author.email,
            date: commit.commit.author.date
          }
        },
        author: commit.author ? {
          login: commit.author.login,
          avatar_url: commit.author.avatar_url
        } : null,
        html_url: commit.html_url
      })),
      pagination: {
        currentPage: parseInt(page),
        perPage: parseInt(per_page),
        totalPages: totalPages,
        totalCommits: totalCommits
      }
    });

  } catch (error) {
    console.error('❌ Erro ao buscar commits:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar commits',
      error_en: 'Error fetching commits',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/insights
 * @desc Busca insights de um repositório específico
 * @access Private
 */
router.get('/repositories/:owner/:repo/insights', authenticateToken, async (req, res) => {
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(401).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided'
      });
    }


    // Buscar informações básicas do repositório
    const repoResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}`, {
      headers: {
        'Authorization': `Bearer ${githubToken}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Code2Post-App'
      }
    });

    if (!repoResponse.ok) {
      if (repoResponse.status === 404) {
        return res.status(404).json({
          error: 'Repositório não encontrado',
          error_en: 'Repository not found'
        });
      }
      throw new Error(`GitHub API error: ${repoResponse.status}`);
    }

    const repoData = await repoResponse.json();

    // Tentar buscar estatísticas de tráfego (views e clones) - pode falhar se não tiver permissão
    let views = null;
    let clones = null;

    try {
      // Views (últimas 2 semanas)
      const viewsResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}/traffic/views`, {
        headers: {
          'Authorization': `Bearer ${githubToken}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Code2Post-App'
        }
      });

      if (viewsResponse.ok) {
        const viewsData = await viewsResponse.json();
        views = viewsData.count || 0;
      }
    } catch (error) {
      console.log('⚠️ Não foi possível obter dados de views (permissão necessária)');
    }

    try {
      // Clones (últimas 2 semanas)
      const clonesResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}/traffic/clones`, {
        headers: {
          'Authorization': `Bearer ${githubToken}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Code2Post-App'
        }
      });

      if (clonesResponse.ok) {
        const clonesData = await clonesResponse.json();
        clones = clonesData.count || 0;
      }
    } catch (error) {
      console.log('⚠️ Não foi possível obter dados de clones (permissão necessária)');
    }

    const insights = {
      size: repoData.size, // Tamanho em KB
      lastPush: repoData.pushed_at,
      defaultBranch: repoData.default_branch,
      openIssues: repoData.open_issues_count,
      watchers: repoData.watchers_count,
      views: views,
      clones: clones
    };

    res.json({
      success: true,
      insights
    });

  } catch (error) {
    console.error('❌ Erro ao buscar insights:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar insights',
      error_en: 'Error fetching insights',
      details: error.message,
    });
  }
});

export default router;
