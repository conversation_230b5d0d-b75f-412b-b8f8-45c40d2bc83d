{"name": "code2post", "version": "1.9.0", "description": "Transform GitHub activity into engaging LinkedIn posts using AI", "scripts": {"env:dev": "node scripts/switch-env.js development", "env:prod": "node scripts/switch-env.js production", "dev": "npm run env:dev && concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "build": "npm run env:prod && cd frontend && npm run build", "start": "npm run env:prod && cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.7.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react": "^4.7.0", "concurrently": "^8.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.6"}, "repository": {"type": "git", "url": "https://github.com/gabrielcamarate/Code2Post.git"}, "keywords": ["github", "linkedin", "automation", "ai", "social-media", "developer-tools"], "author": "<PERSON>", "license": "MIT"}