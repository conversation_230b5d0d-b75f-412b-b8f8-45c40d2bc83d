# Development Environment Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=your_database_url

# JWT (usando as mesmas chaves por enquanto)
JWT_SECRET=681bfc4dd0aa440c0929c30c700f6d58b0edc36ebafbe6ded94aa847394fe0549a75e113f12aecb35bdc84809cf8cd848f47eca8c06a7215b2fd8a520d3de687
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here

# GitHub OAuth - Development
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=72aa8082b06282c0a84e2e1e3fd2ac04be098e2e
GITHUB_CALLBACK_URL=http://localhost:5173/auth/github/callback
# GITHUB_TEST_TOKEN=****************************************
# Gemini AI
GEMINI_API_KEY=AIzaSyDJiOE04xfygIWMFeqzX1-XsyxLoukYN1M

# LinkedIn API (for future use)
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here

# CORS
CORS_ORIGIN=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Debug
DEBUG=true
LOG_LEVEL=debug
