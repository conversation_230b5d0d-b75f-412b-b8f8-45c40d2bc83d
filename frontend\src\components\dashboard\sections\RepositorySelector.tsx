import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  FolderGit2, 
  Star, 
  GitFork, 
  Search, 
  Plus, 
  ExternalLink,
  Calendar,
  Code
} from 'lucide-react';

interface Repository {
  id: string;
  name: string;
  fullName: string;
  description: string;
  language: string;
  stars: number;
  forks: number;
  isPrivate: boolean;
  lastUpdate: string;
  isConnected: boolean;
}

export default function RepositorySelector() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRepo, setSelectedRepo] = useState<string | null>(null);

  // Dados fake para demonstração
  const repositories: Repository[] = [
    {
      id: '1',
      name: 'Code2Post',
      fullName: 'gabrielcamarate/Code2Post',
      description: 'Automatize e compartilhe seu progresso no LinkedIn, diretamente do GitHub, com IA.',
      language: 'TypeScript',
      stars: 15,
      forks: 3,
      isPrivate: false,
      lastUpdate: '2 horas atrás',
      isConnected: true
    },
    {
      id: '2',
      name: 'Portfolio',
      fullName: 'gabrielcamarate/Portfolio',
      description: 'Meu portfólio pessoal desenvolvido com React e Next.js',
      language: 'JavaScript',
      stars: 8,
      forks: 1,
      isPrivate: false,
      lastUpdate: '1 dia atrás',
      isConnected: true
    },
    {
      id: '3',
      name: 'TaskManager',
      fullName: 'gabrielcamarate/TaskManager',
      description: 'Sistema de gerenciamento de tarefas com React e Node.js',
      language: 'TypeScript',
      stars: 12,
      forks: 2,
      isPrivate: false,
      lastUpdate: '3 dias atrás',
      isConnected: false
    },
    {
      id: '4',
      name: 'API-Gateway',
      fullName: 'gabrielcamarate/API-Gateway',
      description: 'Gateway de APIs com autenticação e rate limiting',
      language: 'Python',
      stars: 6,
      forks: 0,
      isPrivate: true,
      lastUpdate: '1 semana atrás',
      isConnected: false
    }
  ];

  const filteredRepos = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repo.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      'TypeScript': 'bg-blue-500',
      'JavaScript': 'bg-yellow-500',
      'Python': 'bg-green-500',
      'Java': 'bg-red-500',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-500'
    };
    return colors[language] || 'bg-slate-500';
  };

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <FolderGit2 className="w-5 h-5 mr-2 text-blue-400" />
          Repositórios GitHub
        </CardTitle>
        <CardDescription className="text-slate-300">
          Conecte repositórios para gerar posts automaticamente
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Search */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            id="repository-search-selector"
            name="repository-search-selector"
            placeholder="Buscar repositórios..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50"
          />
        </div>

        {/* Repository List */}
        <div className="space-y-4">
          {filteredRepos.map((repo) => (
            <div
              key={repo.id}
              className={`
                p-4 rounded-lg border transition-all duration-200 cursor-pointer
                ${selectedRepo === repo.id
                  ? 'bg-blue-500/10 border-blue-500/50'
                  : 'bg-slate-700/30 border-slate-600/30 hover:bg-slate-700/50'
                }
              `}
              onClick={() => setSelectedRepo(repo.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-white font-medium truncate">{repo.name}</h3>
                    {repo.isPrivate && (
                      <Badge variant="outline" className="text-xs text-yellow-400 border-yellow-500/30">
                        Private
                      </Badge>
                    )}
                    {repo.isConnected && (
                      <Badge variant="outline" className="text-xs text-green-400 border-green-500/30">
                        Conectado
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-slate-300 mb-3 line-clamp-2">
                    {repo.description}
                  </p>

                  <div className="flex items-center space-x-4 text-xs text-slate-400">
                    <div className="flex items-center space-x-1">
                      <div className={`w-3 h-3 rounded-full ${getLanguageColor(repo.language)}`}></div>
                      <span>{repo.language}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Star className="w-3 h-3" />
                      <span>{repo.stars}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <GitFork className="w-3 h-3" />
                      <span>{repo.forks}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{repo.lastUpdate}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-slate-400 hover:text-white"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                  
                  {repo.isConnected ? (
                    <Button
                      size="sm"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      <Code className="w-4 h-4 mr-1" />
                      Gerar Post
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Conectar
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredRepos.length === 0 && (
          <div className="text-center py-8">
            <FolderGit2 className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-400">Nenhum repositório encontrado</p>
          </div>
        )}

        {/* Connect New Repository */}
        <div className="mt-6 pt-6 border-t border-slate-600/30">
          <Button className="w-full bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700">
            <Plus className="w-4 h-4 mr-2" />
            Conectar Novo Repositório
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
