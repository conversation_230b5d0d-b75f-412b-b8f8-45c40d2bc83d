import jwt from 'jsonwebtoken';

// Blacklist de tokens (importar do arquivo de rotas)
let tokenBlacklist = new Set();

// Função para atualizar a blacklist (ser<PERSON> chamada pelo arquivo de rotas)
const atualizarBlacklist = blacklist => {
  tokenBlacklist = blacklist;
};

// Middleware para verificar se o token é válido
const authenticateToken = (req, res, next) => {
  // Pegar o token do header Authorization
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: 'Token não fornecido' });
  }

  // Verificar se o token está na blacklist
  if (tokenBlacklist.has(token)) {
    return res.status(401).json({ error: 'Token revogado' });
  }

  // Para desenvolvimento: aceitar token de teste (COMENTADO PARA TESTES REAIS)
  // if (process.env.NODE_ENV === 'development' && token === 'test-jwt-token-for-development') {
  //   console.log('🧪 Token de teste aceito para desenvolvimento');
  //   req.user = {
  //     userId: '1',
  //     email: '<EMAIL>',
  //     name: 'Gabriel Camarate'
  //   };
  //   return next();
  // }

  try {
    // Verificar o token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded; // Adicionar dados do usuário na requisição
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Token inválido' });
  }
};

export { authenticateToken, atualizarBlacklist };
