import { useState } from 'react';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Calendar,
  Clock,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  BarChart3,
  Users,
  Heart,
  MessageCircle,
  Share,
  Send,
  Filter,
  Search,
  MoreHorizontal,
  GitBranch,
  Tag,
  X,
  Save
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ScheduledPost {
  id: string;
  title: string;
  content: string;
  scheduledDate: string;
  scheduledTime: string;
  status: 'scheduled' | 'published' | 'failed' | 'draft';
  repository: string;
  type: 'milestone' | 'feature' | 'update' | 'bugfix';
  hashtags: string[];
  metrics?: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
}

export default function Scheduling() {
  const [posts, setPosts] = useState<ScheduledPost[]>([
    {
      id: '1',
      title: '🚀 TaskManager - Sistema de Gerenciamento Completo',
      content: 'Trabalhando em um novo projeto: TaskManager! 🛠️ Um sistema completo de gerenciamento de tarefas com funcionalidades avançadas. 📊\n\n#TaskManager #Produtividade #React #NodeJS #MongoDB',
      scheduledDate: '2025-01-28',
      scheduledTime: '14:30',
      status: 'scheduled',
      repository: 'TaskManager',
      type: 'feature',
      hashtags: ['TaskManager', 'Produtividade', 'React', 'NodeJS', 'MongoDB'],
      metrics: {
        views: 1250,
        likes: 89,
        comments: 12,
        shares: 23
      }
    },
    {
      id: '2',
      title: '❌ E-commerce API - Falha na Publicação',
      content: 'Desenvolvendo uma API REST robusta para e-commerce! 🛒 Funcionalidades implementadas hoje: • Sistema de autenticação completo • Gerenciamento de produtos • Carrinho de compras • Processamento de pagamentos\n\n#EcommerceAPI #NodeJS #MongoDB #API #Backend',
      scheduledDate: '2025-01-27',
      scheduledTime: '10:00',
      status: 'failed',
      repository: 'E-commerce-API',
      type: 'feature',
      hashtags: ['EcommerceAPI', 'NodeJS', 'MongoDB', 'API', 'Backend']
    },
    {
      id: '3',
      title: '✅ Portfolio Website - Publicado com Sucesso',
      content: 'Acabei de finalizar meu novo portfolio! ✨ Tecnologias utilizadas: • React + TypeScript • Tailwind CSS • Framer Motion • Vercel Deploy\n\nConfira em: portfolio.dev 🔗\n\n#Portfolio #React #TypeScript #WebDev',
      scheduledDate: '2025-01-26',
      scheduledTime: '16:45',
      status: 'published',
      repository: 'Portfolio',
      type: 'milestone',
      hashtags: ['Portfolio', 'React', 'TypeScript', 'WebDev'],
      metrics: {
        views: 2340,
        likes: 156,
        comments: 28,
        shares: 45
      }
    }
  ]);

  const [editingPost, setEditingPost] = useState<ScheduledPost | null>(null);
  const [showNewPostForm, setShowNewPostForm] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'published': return 'bg-green-500/10 text-green-400 border-green-500/30';
      case 'failed': return 'bg-red-500/10 text-red-400 border-red-500/30';
      case 'draft': return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <Clock className="w-3 h-3" />;
      case 'published': return <CheckCircle className="w-3 h-3" />;
      case 'failed': return <XCircle className="w-3 h-3" />;
      case 'draft': return <AlertCircle className="w-3 h-3" />;
      default: return <Clock className="w-3 h-3" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone': return 'bg-purple-500/10 text-purple-400 border-purple-500/30';
      case 'feature': return 'bg-green-500/10 text-green-400 border-green-500/30';
      case 'update': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'bugfix': return 'bg-red-500/10 text-red-400 border-red-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const filteredPosts = posts.filter(post => {
    const matchesStatus = filterStatus === 'all' || post.status === filterStatus;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.repository.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const stats = {
    total: posts.length,
    scheduled: posts.filter(p => p.status === 'scheduled').length,
    published: posts.filter(p => p.status === 'published').length,
    failed: posts.filter(p => p.status === 'failed').length,
    draft: posts.filter(p => p.status === 'draft').length
  };

  const handleSavePost = () => {
    if (editingPost) {
      // Update existing post
      setPosts(posts.map(p => p.id === editingPost.id ? editingPost : p));
    } else {
      // Create new post
      const newPost: ScheduledPost = {
        id: Date.now().toString(),
        title: '',
        content: '',
        scheduledDate: new Date().toISOString().split('T')[0],
        scheduledTime: '12:00',
        status: 'draft',
        repository: 'Code2Post',
        type: 'feature',
        hashtags: []
      };
      setPosts([...posts, newPost]);
      setEditingPost(newPost);
    }
  };

  const handleCloseModal = () => {
    setEditingPost(null);
    setShowNewPostForm(false);
  };

  return (
    <>
      {/* Edit/Create Post Modal */}
      <Dialog open={editingPost !== null || showNewPostForm} onOpenChange={handleCloseModal}>
        <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingPost ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
              {editingPost ? 'Editar Post' : 'Novo Post'}
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              {editingPost ? 'Modifique as informações do post agendado' : 'Crie um novo post para agendamento'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Title */}
            <div>
              <Label className="text-slate-300">Título do Post</Label>
              <Input
                value={editingPost?.title || ''}
                onChange={(e) => setEditingPost(prev => prev ? {...prev, title: e.target.value} : null)}
                placeholder="Ex: 🚀 Novo projeto incrível..."
                className="bg-slate-800 border-slate-700 text-white placeholder-slate-400"
              />
            </div>

            {/* Content */}
            <div>
              <Label className="text-slate-300">Conteúdo</Label>
              <Textarea
                value={editingPost?.content || ''}
                onChange={(e) => setEditingPost(prev => prev ? {...prev, content: e.target.value} : null)}
                placeholder="Descreva seu projeto, conquistas e tecnologias utilizadas..."
                rows={6}
                className="bg-slate-800 border-slate-700 text-white placeholder-slate-400 resize-none"
              />
              <p className="text-xs text-slate-400 mt-1">
                {editingPost?.content?.length || 0}/3000 caracteres
              </p>
            </div>

            {/* Repository and Type */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label className="text-slate-300">Repositório</Label>
                <Input
                  value={editingPost?.repository || ''}
                  onChange={(e) => setEditingPost(prev => prev ? {...prev, repository: e.target.value} : null)}
                  placeholder="Nome do repositório"
                  className="bg-slate-800 border-slate-700 text-white placeholder-slate-400"
                />
              </div>
              <div>
                <Label className="text-slate-300">Tipo</Label>
                <Select
                  value={editingPost?.type || 'feature'}
                  onValueChange={(value) => setEditingPost(prev => prev ? {...prev, type: value as any} : null)}
                >
                  <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="milestone">Milestone</SelectItem>
                    <SelectItem value="feature">Feature</SelectItem>
                    <SelectItem value="update">Update</SelectItem>
                    <SelectItem value="bugfix">Bugfix</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label className="text-slate-300">Data</Label>
                <Input
                  type="date"
                  value={editingPost?.scheduledDate || ''}
                  onChange={(e) => setEditingPost(prev => prev ? {...prev, scheduledDate: e.target.value} : null)}
                  className="bg-slate-800 border-slate-700 text-white"
                />
              </div>
              <div>
                <Label className="text-slate-300">Horário</Label>
                <Input
                  type="time"
                  value={editingPost?.scheduledTime || ''}
                  onChange={(e) => setEditingPost(prev => prev ? {...prev, scheduledTime: e.target.value} : null)}
                  className="bg-slate-800 border-slate-700 text-white"
                />
              </div>
            </div>

            {/* Hashtags */}
            <div>
              <Label className="text-slate-300">Hashtags</Label>
              <Input
                value={editingPost?.hashtags?.join(', ') || ''}
                onChange={(e) => setEditingPost(prev => prev ? {
                  ...prev,
                  hashtags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                } : null)}
                placeholder="React, TypeScript, WebDev, NodeJS"
                className="bg-slate-800 border-slate-700 text-white placeholder-slate-400"
              />
              <p className="text-xs text-slate-400 mt-1">
                Separe as hashtags com vírgulas
              </p>
            </div>

            {/* Status */}
            <div>
              <Label className="text-slate-300">Status</Label>
              <Select
                value={editingPost?.status || 'draft'}
                onValueChange={(value) => setEditingPost(prev => prev ? {...prev, status: value as any} : null)}
              >
                <SelectTrigger className="bg-slate-800 border-slate-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button
                onClick={handleSavePost}
                className="flex-1 bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {editingPost ? 'Salvar Alterações' : 'Criar Post'}
              </Button>
              <Button
                onClick={handleCloseModal}
                variant="outline"
                className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <DashboardLayout>
      <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent leading-tight">
              Agendamento de Posts
            </h1>
            <p className="text-slate-300 mt-1 sm:mt-2 text-sm sm:text-base">
              Gerencie e agende seus posts do LinkedIn automaticamente
            </p>
          </div>
          
          <Button
            onClick={() => setShowNewPostForm(true)}
            size="sm"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 w-full sm:w-auto"
          >
            <Plus className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Novo Post</span>
            <span className="sm:hidden">Novo</span>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4">
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-xs sm:text-sm">Total</p>
                  <p className="text-xl sm:text-2xl font-bold text-white">{stats.total}</p>
                </div>
                <Calendar className="w-6 h-6 sm:w-8 sm:h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-xs sm:text-sm">Agendados</p>
                  <p className="text-xl sm:text-2xl font-bold text-blue-400">{stats.scheduled}</p>
                </div>
                <Clock className="w-6 h-6 sm:w-8 sm:h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-xs sm:text-sm">Publicados</p>
                  <p className="text-xl sm:text-2xl font-bold text-green-400">{stats.published}</p>
                </div>
                <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-xs sm:text-sm">Falharam</p>
                  <p className="text-xl sm:text-2xl font-bold text-red-400">{stats.failed}</p>
                </div>
                <XCircle className="w-6 h-6 sm:w-8 sm:h-8 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-xs sm:text-sm">Rascunhos</p>
                  <p className="text-xl sm:text-2xl font-bold text-yellow-400">{stats.draft}</p>
                </div>
                <AlertCircle className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Buscar posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-700 text-white placeholder-slate-400"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            {['all', 'scheduled', 'published', 'failed', 'draft'].map((status) => (
              <Button
                key={status}
                variant={filterStatus === status ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus(status)}
                className={`${
                  filterStatus === status
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600'
                    : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                } capitalize`}
              >
                {status === 'all' ? 'Todos' : status}
              </Button>
            ))}
          </div>
        </div>

        {/* Posts List */}
        <div className="space-y-4">
          {filteredPosts.map((post) => (
            <Card key={post.id} className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 hover:border-slate-600/50 transition-all duration-200">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col lg:flex-row lg:items-start gap-4">
                  {/* Post Content */}
                  <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                      <h3 className="text-white font-semibold text-sm sm:text-base leading-tight">
                        {post.title}
                      </h3>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Badge className={`${getStatusColor(post.status)} text-xs`}>
                          {getStatusIcon(post.status)}
                          <span className="ml-1 capitalize">{post.status}</span>
                        </Badge>
                        <Badge className={`${getTypeColor(post.type)} text-xs`}>
                          <Tag className="w-3 h-3 mr-1" />
                          {post.type}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-slate-300 text-sm line-clamp-3">
                      {post.content}
                    </p>

                    <div className="flex flex-wrap items-center gap-2 text-xs text-slate-400">
                      <div className="flex items-center gap-1">
                        <GitBranch className="w-3 h-3" />
                        {post.repository}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(post.scheduledDate).toLocaleDateString('pt-BR')}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {post.scheduledTime}
                      </div>
                    </div>

                    {/* Hashtags */}
                    <div className="flex flex-wrap gap-1">
                      {post.hashtags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="text-blue-400 text-xs bg-blue-500/10 px-2 py-1 rounded">
                          #{tag}
                        </span>
                      ))}
                      {post.hashtags.length > 3 && (
                        <span className="text-slate-400 text-xs">
                          +{post.hashtags.length - 3} mais
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Metrics & Actions */}
                  <div className="flex flex-col gap-3 lg:w-48">
                    {/* Metrics */}
                    {post.metrics && post.status === 'published' && (
                      <div className="grid grid-cols-2 gap-2 p-3 bg-slate-900/50 rounded-lg">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-slate-400 text-xs">
                            <Eye className="w-3 h-3" />
                            Views
                          </div>
                          <div className="text-white font-semibold text-sm">{post.metrics.views.toLocaleString()}</div>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-slate-400 text-xs">
                            <Heart className="w-3 h-3" />
                            Likes
                          </div>
                          <div className="text-white font-semibold text-sm">{post.metrics.likes}</div>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-slate-400 text-xs">
                            <MessageCircle className="w-3 h-3" />
                            Comments
                          </div>
                          <div className="text-white font-semibold text-sm">{post.metrics.comments}</div>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 text-slate-400 text-xs">
                            <Share className="w-3 h-3" />
                            Shares
                          </div>
                          <div className="text-white font-semibold text-sm">{post.metrics.shares}</div>
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingPost(post)}
                          className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">Editar</span>
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">Preview</span>
                        </Button>
                      </div>

                      {post.status === 'scheduled' && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                          >
                            <Play className="w-3 h-3 mr-1" />
                            <span className="hidden sm:inline">Publicar</span>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1 border-yellow-600 text-yellow-400 hover:bg-yellow-600/10"
                          >
                            <Pause className="w-3 h-3 mr-1" />
                            <span className="hidden sm:inline">Pausar</span>
                          </Button>
                        </div>
                      )}

                      {post.status === 'failed' && (
                        <Button
                          size="sm"
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          <Send className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">Tentar Novamente</span>
                          <span className="sm:hidden">Retry</span>
                        </Button>
                      )}

                      <Button
                        size="sm"
                        variant="ghost"
                        className="w-full text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Excluir</span>
                        <span className="sm:hidden">Del</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredPosts.length === 0 && (
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardContent className="p-8 text-center">
                <Calendar className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-white font-semibold mb-2">Nenhum post encontrado</h3>
                <p className="text-slate-400 text-sm mb-4">
                  {searchTerm || filterStatus !== 'all'
                    ? 'Tente ajustar os filtros de busca'
                    : 'Comece criando seu primeiro post agendado'
                  }
                </p>
                {!searchTerm && filterStatus === 'all' && (
                  <Button
                    onClick={() => setShowNewPostForm(true)}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Criar Primeiro Post
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
    </>
  );
}
