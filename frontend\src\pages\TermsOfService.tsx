import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Sparkles, Code, Zap, ArrowLeft, FileText } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function TermsOfService() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
          <Code size={24} />
        </div>
        <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
          <Sparkles size={24} />
        </div>
        <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
          <Zap size={24} />
        </div>
      </div>

      <div className="relative z-10 min-h-screen p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-4 pt-8">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Termos de Serviço
              </h1>
            </div>
            <p className="text-slate-300 text-lg">
              Última atualização: 19 de Janeiro de 2025
            </p>
          </div>

          {/* Back Button */}
          <div className="flex justify-start">
            <Link to="/">
              <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Voltar ao Início
              </Button>
            </Link>
          </div>

          {/* Terms Content */}
          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">1. Aceitação dos Termos</CardTitle>
              <CardDescription className="text-slate-300">
                Ao acessar e usar o Code2Post, você aceita estar vinculado a estes Termos de Serviço.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Bem-vindo ao Code2Post. Estes Termos de Serviço ("Termos") regem seu uso do nosso serviço 
                  de automação de posts para LinkedIn baseado em atividades do GitHub.
                </p>
                
                <p>
                  Ao usar nosso serviço, você concorda com estes termos. Se você não concordar com qualquer 
                  parte destes termos, não poderá acessar o serviço.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">2. Descrição do Serviço</CardTitle>
              <CardDescription className="text-slate-300">
                O Code2Post é uma plataforma que conecta seu GitHub ao LinkedIn para gerar posts automáticos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  O Code2Post oferece as seguintes funcionalidades:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Integração com GitHub OAuth para acessar seus repositórios</li>
                  <li>Análise automática de commits e atividades de desenvolvimento</li>
                  <li>Geração de posts para LinkedIn usando inteligência artificial</li>
                  <li>Dashboard para gerenciar e visualizar posts gerados</li>
                  <li>Personalização de templates e estilos de post</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">3. Conta do Usuário</CardTitle>
              <CardDescription className="text-slate-300">
                Responsabilidades e obrigações relacionadas à sua conta.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Para usar o Code2Post, você deve:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Ter pelo menos 18 anos de idade ou ter consentimento parental</li>
                  <li>Fornecer informações precisas e atualizadas</li>
                  <li>Manter a segurança de suas credenciais de acesso</li>
                  <li>Ser responsável por todas as atividades em sua conta</li>
                  <li>Notificar-nos imediatamente sobre uso não autorizado</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">4. Uso Aceitável</CardTitle>
              <CardDescription className="text-slate-300">
                Diretrizes para o uso apropriado da plataforma.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>Você concorda em não:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Usar o serviço para atividades ilegais ou fraudulentas</li>
                  <li>Violar direitos de propriedade intelectual</li>
                  <li>Transmitir vírus, malware ou código malicioso</li>
                  <li>Tentar acessar sistemas não autorizados</li>
                  <li>Interferir no funcionamento do serviço</li>
                  <li>Usar o serviço para spam ou conteúdo inadequado</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">5. Privacidade e Dados</CardTitle>
              <CardDescription className="text-slate-300">
                Como tratamos seus dados pessoais e informações.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Sua privacidade é importante para nós. Nossa coleta e uso de dados pessoais 
                  são regidos por nossa Política de Privacidade, que faz parte destes Termos.
                </p>
                <p>
                  Ao usar o Code2Post, você autoriza:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Acesso aos seus repositórios GitHub (apenas repositórios públicos)</li>
                  <li>Análise de commits e atividades de desenvolvimento</li>
                  <li>Geração de conteúdo baseado em suas atividades</li>
                  <li>Armazenamento seguro de tokens de acesso</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">6. Propriedade Intelectual</CardTitle>
              <CardDescription className="text-slate-300">
                Direitos sobre o conteúdo e propriedade intelectual.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  O Code2Post e seu conteúdo original, recursos e funcionalidades são e permanecerão 
                  propriedade exclusiva da empresa e seus licenciadores.
                </p>
                <p>
                  Você mantém a propriedade do conteúdo que você cria e compartilha através do serviço, 
                  mas concede ao Code2Post uma licença não exclusiva para usar esse conteúdo para 
                  fornecer o serviço.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">7. Limitação de Responsabilidade</CardTitle>
              <CardDescription className="text-slate-300">
                Limitações sobre nossa responsabilidade legal.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Em nenhuma circunstância o Code2Post será responsável por danos indiretos, 
                  incidentais, especiais, consequenciais ou punitivos, incluindo perda de lucros, 
                  dados ou uso.
                </p>
                <p>
                  Nossa responsabilidade total será limitada ao valor pago por você pelo serviço 
                  nos 12 meses anteriores ao evento que deu origem à responsabilidade.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">8. Modificações dos Termos</CardTitle>
              <CardDescription className="text-slate-300">
                Como e quando podemos modificar estes termos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Reservamo-nos o direito de modificar estes Termos a qualquer momento. 
                  As modificações entrarão em vigor imediatamente após a publicação.
                </p>
                <p>
                  Seu uso contínuo do serviço após as modificações constitui aceitação dos novos Termos.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">9. Contato</CardTitle>
              <CardDescription className="text-slate-300">
                Como entrar em contato conosco sobre estes termos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Se você tiver dúvidas sobre estes Termos de Serviço, entre em contato conosco:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Email: <EMAIL></li>
                  <li>Website: https://code2post.com</li>
                  <li>GitHub: https://github.com/gabrielcamarate/Code2Post</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center space-y-4 pb-8">
            <p className="text-slate-400 text-sm">
              <Link to="/privacy-policy" className="text-blue-400 hover:text-blue-300 transition-colors">
                Política de Privacidade
              </Link>
              {' • '}
              <Link to="/cookie-policy" className="text-blue-400 hover:text-blue-300 transition-colors">
                Política de Cookies
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 
