import { GoogleGenerativeAI } from '@google/generative-ai';

// Configuração da Gemini API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Modelo Gemini 2.5 Flash para geração de texto (mais rápido e eficiente)
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

/**
 * Gera um post para LinkedIn baseado nos commits do GitHub
 * @param {Array} commits - Array de commits do GitHub
 * @param {Object} userInfo - Informações do usuário
 * @returns {Promise<string>} Post gerado para LinkedIn
 */
async function generateLinkedInPost(commits, userInfo) {
  try {
    // Cria o prompt baseado nos commits
    const commitSummary = commits
      .map(
        commit => `- ${commit.commit.message} (${commit.sha.substring(0, 7)})`
      )
      .join('\n');

    const prompt = `
Você é um especialista em marketing de conteúdo técnico. 
Crie um post envolvente para LinkedIn baseado nas seguintes atividades de desenvolvimento:

**Usuário:** ${userInfo.login}
**Repositório:** ${userInfo.repoName || 'Projeto pessoal'}
**Atividades recentes:**
${commitSummary}

**Instruções:**
- Crie um post profissional e técnico
- Destaque as tecnologias e soluções implementadas
- Use linguagem acessível para desenvolvedores
- Inclua hashtags relevantes
- Mantenha o tom positivo e construtivo
- Limite a 1300 caracteres (limite do LinkedIn)
- Use emojis moderadamente para tornar mais atrativo

**Formato desejado:**
- Título chamativo
- Conteúdo técnico envolvente
- Hashtags relevantes
- Call-to-action sutil

Gere apenas o post, sem explicações adicionais.
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error('Erro ao gerar post com Gemini:', error);
    throw new Error('Falha ao gerar conteúdo com IA');
  }
}

/**
 * Gera um resumo técnico dos commits
 * @param {Array} commits - Array de commits do GitHub
 * @returns {Promise<string>} Resumo técnico
 */
async function generateTechnicalSummary(commits) {
  try {
    const commitDetails = commits
      .map(
        commit => `- ${commit.commit.message} (${commit.sha.substring(0, 7)})`
      )
      .join('\n');

    const prompt = `
Analise os seguintes commits de desenvolvimento e crie um resumo técnico conciso:

${commitDetails}

**Instruções:**
- Identifique as principais funcionalidades implementadas
- Destaque as tecnologias utilizadas
- Mencione melhorias ou correções importantes
- Seja técnico mas acessível
- Limite a 500 caracteres

Gere apenas o resumo, sem explicações adicionais.
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error('Erro ao gerar resumo técnico:', error);
    throw new Error('Falha ao gerar resumo técnico');
  }
}

/**
 * Gera hashtags relevantes baseadas nos commits
 * @param {Array} commits - Array de commits do GitHub
 * @returns {Promise<Array>} Array de hashtags
 */
async function generateHashtags(commits) {
  try {
    const commitMessages = commits
      .map(commit => commit.commit.message)
      .join(' ');

    const prompt = `
Com base nas seguintes mensagens de commit, gere 5-8 hashtags relevantes para LinkedIn:

${commitMessages}

**Instruções:**
- Use hashtags populares na comunidade tech
- Inclua tecnologias mencionadas
- Mantenha relevância para desenvolvedores
- Evite hashtags muito genéricas
- Retorne apenas as hashtags, uma por linha, sem # no início

Exemplo de formato:
javascript
react
webdevelopment
coding
opensource
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;

    // Processa a resposta para extrair hashtags
    const hashtags = response
      .text()
      .split('\n')
      .filter(tag => tag.trim())
      .map(tag => tag.trim().replace(/^#/, ''))
      .slice(0, 8); // Limita a 8 hashtags

    return hashtags;
  } catch (error) {
    console.error('Erro ao gerar hashtags:', error);
    // Retorna hashtags padrão em caso de erro
    return ['coding', 'webdevelopment', 'javascript', 'opensource'];
  }
}

export { generateLinkedInPost, generateTechnicalSummary, generateHashtags };
