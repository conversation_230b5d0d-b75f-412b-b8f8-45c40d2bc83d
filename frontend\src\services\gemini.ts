import api from './api';

export interface PostGenerationRequest {
  commitMessage: string;
  repositoryName: string;
  language: string;
  description?: string;
  tone?: 'professional' | 'casual' | 'technical' | 'enthusiastic';
  platform?: 'linkedin' | 'twitter' | 'medium';
}

export interface PostGenerationResponse {
  id: string;
  content: string;
  title?: string;
  hashtags?: string[];
  suggestedImage?: string;
  estimatedEngagement?: number;
  createdAt: string;
}

export interface PostTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: string[];
}

class GeminiService {
  // Gerar post baseado em commit
  async generatePost(request: PostGenerationRequest): Promise<PostGenerationResponse> {
    const response = await api.post<PostGenerationResponse>('/gemini/generate-post', request);
    return response.data;
  }

  // Gerar post com template específico
  async generatePostWithTemplate(
    templateId: string, 
    variables: Record<string, string>
  ): Promise<PostGenerationResponse> {
    const response = await api.post<PostGenerationResponse>('/gemini/generate-with-template', {
      templateId,
      variables
    });
    return response.data;
  }

  // Buscar templates disponíveis
  async getTemplates(): Promise<PostTemplate[]> {
    const response = await api.get<PostTemplate[]>('/gemini/templates');
    return response.data;
  }

  // Criar novo template
  async createTemplate(template: Omit<PostTemplate, 'id'>): Promise<PostTemplate> {
    const response = await api.post<PostTemplate>('/gemini/templates', template);
    return response.data;
  }

  // Atualizar template
  async updateTemplate(id: string, template: Partial<PostTemplate>): Promise<PostTemplate> {
    const response = await api.put<PostTemplate>(`/gemini/templates/${id}`, template);
    return response.data;
  }

  // Deletar template
  async deleteTemplate(id: string): Promise<void> {
    await api.delete(`/gemini/templates/${id}`);
  }

  // Analisar engajamento de um post
  async analyzeEngagement(content: string): Promise<{
    score: number;
    suggestions: string[];
    hashtags: string[];
  }> {
    const response = await api.post('/gemini/analyze-engagement', { content });
    return response.data;
  }

  // Gerar hashtags relevantes
  async generateHashtags(content: string, count: number = 5): Promise<string[]> {
    const response = await api.post('/gemini/generate-hashtags', { content, count });
    return response.data.hashtags;
  }

  // Otimizar post para melhor engajamento
  async optimizePost(content: string, platform: string): Promise<{
    optimizedContent: string;
    improvements: string[];
    score: number;
  }> {
    const response = await api.post('/gemini/optimize-post', { content, platform });
    return response.data;
  }
}

export default new GeminiService(); 