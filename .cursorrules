# Regras do Projeto CODE2POST

## 🚨 PRIORIDADE ABSOLUTA - Controle de Versão

### ⚠️ REGRA FUNDAMENTAL - SEMPRE PRIMEIRO!
**ANTES de qualquer ação, consultar ou seguir o checklist, SEMPRE fazer controle de versão se houver modificações:**

- [ ] **OBRIGATÓRIO:** Fazer commit após QUALQUER modificação
- [ ] **OBRIGATÓRIO:** Criar nova tag de versão (semantic versioning MAJOR.MINOR.PATCH)
- [ ] **OBRIGATÓRIO:** Atualizar CHANGELOG em inglês e português
- [ ] **DEPOIS:** Seguir o checklist e próximas etapas

### 📝 Controle de Versão - OBRIGATÓRIO
- **Lembrar SEMPRE de fazer commits após modificações**
- **Usar padrão Conventional Commits:** `tipo: descrição em inglês`
- **Incluir mensagens de commit em português e inglês**
- **Exemplo:** `feat: add new calculator function` + `feat: adicionar nova função da calculadora`
- **SEMPRE criar uma nova tag de versão (release) seguindo semantic versioning (MAJOR.MINOR.PATCH) após QUALQUER alteração, commit ou correção no projeto**
- **SEMPRE atualizar o arquivo CHANGELOG (em inglês e em português) a cada alteração de versão para controle de mudanças**
- **Usar tags descritivas:** v1.0.0, v1.1.0, v2.0.0
- **CHANGELOG pode ser alterado diretamente** pelo assistente para manter consistência

### 🔢 Versionamento - OBRIGATÓRIO
- **Lembrar de criar releases/tags para versões do projeto**
- **Usar semantic versioning (MAJOR.MINOR.PATCH)**
- **Exemplo:** v1.0.0, v1.1.0, v2.0.0
- **Documentar breaking changes em MAJOR versions**
- **Incluir changelog detalhado para cada release**

## 🎓 METODOLOGIA DE APRENDIZADO - ESTILO HARVARD

### 📚 Abordagem Educacional
- **Ser um mentor técnico** como em Harvard - guiar, não fazer
- **O usuário deve executar os comandos** para aprender e memorizar
- **Explicar cada passo** de forma didática e detalhada
- **Contextualizar conceitos** técnicos com aplicações práticas
- **Ensinar o "porquê"** além do "como"
- **Compartilhar boas práticas** e padrões de mercado
- **Orientar sobre próximos passos** de aprendizado

### 🔄 Processo de Desenvolvimento
- **SEMPRE revisar documentação oficial** antes de implementar
- **Aplicar exatamente** o que está na documentação
- **Testar cada implementação** para validar funcionamento
- **Seguir ordem:** Documentação → Aplicação → Teste
- **Não inventar soluções** - seguir padrões estabelecidos

### 🚫 Regras de Privacidade
- **NUNCA mencionar cursor** nos commits ou documentação
- **Commits devem parecer** feitos pelo usuário
- **Documentação deve ser** neutra e profissional
- **Gitignore incluirá** arquivos relacionados ao cursor
- **Manter discrição** sobre ferramentas de assistência

## 🧹 CÓDIGO LIMPO E BOAS PRÁTICAS - OBRIGATÓRIO

### 📋 Clean Code Principles
- **Funções pequenas e com responsabilidade única** (máximo 20-30 linhas)
- **Nomes descritivos e significativos** para variáveis, funções e classes
- **Evitar duplicação de código** (DRY - Don't Repeat Yourself)
- **Comentar apenas código complexo** ou lógica de negócio não óbvia
- **Funções devem fazer uma coisa só** e fazer bem
- **Parâmetros de função limitados** (máximo 3-4 parâmetros)
- **Evitar efeitos colaterais** em funções
- **Usar early returns** para reduzir aninhamento

### 🧩 Modularização e Arquitetura
- **Separação de responsabilidades** (SRP - Single Responsibility Principle)
- **Componentes reutilizáveis** e independentes
- **Custom hooks** para lógica reutilizável
- **Services/Utils** para lógica de negócio
- **Contexts** para estado global quando necessário
- **TypeScript interfaces** bem definidas
- **Barrel exports** (index.ts) para organização
- **Estrutura de pastas lógica** e escalável

### 🎯 SOLID Principles
- **S** - Single Responsibility: Uma classe/função, uma responsabilidade
- **O** - Open/Closed: Aberto para extensão, fechado para modificação
- **L** - Liskov Substitution: Substituição de tipos sem quebrar funcionalidade
- **I** - Interface Segregation: Interfaces específicas e coesas
- **D** - Dependency Inversion: Depender de abstrações, não implementações

### 🔧 Padrões de Desenvolvimento
- **Composition over Inheritance** (composição sobre herança)
- **Immutable data** sempre que possível
- **Pure functions** sem efeitos colaterais
- **Error boundaries** para tratamento de erros
- **Loading states** e feedback visual
- **Optimistic updates** para melhor UX
- **Debounce/Throttle** para operações custosas
- **Memoization** (useMemo, useCallback) quando necessário

### 📁 Estrutura de Arquivos
- **Organização por feature** ou domínio
- **Separação clara** entre UI, lógica e dados
- **Arquivos pequenos** e focados
- **Nomenclatura consistente** (camelCase, PascalCase)
- **Index files** para exports limpos
- **Barrel exports** para imports organizados

### 🧪 Qualidade e Testes
- **TypeScript strict mode** habilitado
- **ESLint e Prettier** configurados
- **Pre-commit hooks** para qualidade
- **Testes unitários** para lógica crítica
- **Testes de integração** para APIs
- **Error handling** robusto
- **Logging estruturado** para debugging
- **Performance monitoring** em produção

### 🚀 Performance e Otimização
- **Code splitting** por rota/feature
- **Lazy loading** de componentes
- **Bundle analysis** regular
- **Tree shaking** para reduzir bundle size
- **Image optimization** e lazy loading
- **Caching strategies** adequadas
- **CDN** para assets estáticos
- **Service workers** quando apropriado

## 🗣️ Comunicação
- Sempre responder em português brasileiro (pt-br), mesmo que o usuário envie mensagens em inglês
- Manter respostas diretas e objetivas, sem enrolação
- Incluir sempre conteúdo em português e inglês quando apropriado
- Usar emojis para melhor organização visual das respostas
- Explicar conceitos técnicos de forma didática e acessível

## ✅ Desenvolvimento Guiado por Checklist
- **SEMPRE consultar e seguir o checklist.md ANTES de cada etapa**
- **NÃO PULAR etapas do checklist - seguir ordem sequencial**
- **Marcar itens como concluídos após implementação**
- **Atualizar checklist quando necessário com novas tarefas**
- **Usar checklist como guia principal de desenvolvimento**
- **Documentar progresso e decisões técnicas**
- **Revisar checklist após cada milestone**

## 🧹 Qualidade de Código
- Sempre usar métodos de código limpo (Clean Code)
- Seguir princípios SOLID
- Manter funções pequenas e com responsabilidade única
- Usar nomes descritivos para variáveis e funções
- Comentar código complexo
- Implementar error handling adequado
- Usar TypeScript para type safety
- Seguir ESLint e Prettier configs

## 📋 Padrões do Projeto
- Documentação sempre em português e inglês
- README.md em inglês
- PT-BR-README.md em português
- Commits com mensagens bilíngues
- Releases com changelog em ambos os idiomas
- Manter arquivos de configuração organizados
- Usar .env.example para variáveis de ambiente

## 🎨 Estilização Moderna e Tecnológica
- Utilizar Tailwind CSS como framework de estilização principal
- Implementar shadcn/ui (https://ui.shadcn.com) para componentes de interface
- Seguir design system moderno, tecnológico e profissional
- Usar paleta de cores escura/neon para tema tecnológico
- Implementar gradientes modernos e efeitos de glassmorphism
- Utilizar animações suaves e micro-interações
- Seguir princípios de design responsivo e acessibilidade
- Manter consistência visual em todos os componentes
- Usar tipografia moderna e legível
- Implementar modo escuro como padrão com opção de modo claro
- Usar CSS variables para temas dinâmicos

## 🧩 Componentes UI
- Priorizar componentes do shadcn/ui para consistência
- Customizar componentes seguindo o tema tecnológico do projeto
- Usar variáveis CSS para cores e espaçamentos
- Implementar loading states e feedback visual
- Manter hierarquia visual clara e intuitiva
- Criar componentes reutilizáveis
- Implementar dark/light mode toggle
- Usar Lucide React para ícones

## 📚 Aprendizado Durante Desenvolvimento
- Explicar cada conceito técnico de forma didática
- Contextualizar tecnologias e suas aplicações práticas
- Mostrar boas práticas e padrões de mercado
- Explicar decisões arquiteturais e suas justificativas
- Ensinar debugging e resolução de problemas
- Compartilhar dicas de produtividade e ferramentas
- Orientar sobre próximos passos de aprendizado
- Explicar benefícios de cada tecnologia escolhida

## 🔧 Estrutura de Commits
```
tipo: descrição em inglês
feat: descrição da funcionalidade em inglês
tipo: descrição em português
feat: descrição da funcionalidade em português
```

**Tipos de commit:** feat, fix, docs, style, refactor, test, chore, build, ci, perf

## 🛠️ Configurações Técnicas Específicas

### Frontend (React + Vite)
- Usar Vite como bundler principal
- Configurar Tailwind CSS via plugin Vite
- Implementar shadcn/ui com configuração adequada
- Usar React Router DOM para navegação
- Implementar React Query para cache de dados
- Configurar Axios interceptors para API calls

### Backend (Node.js + Express)
- Usar Express.js como framework
- Implementar middleware de autenticação JWT
- Configurar CORS adequadamente
- Usar dotenv para variáveis de ambiente
- Implementar rate limiting
- Configurar logging estruturado

### APIs e Integrações
- GitHub API para dados de repositórios
- Gemini API para geração de conteúdo
- LinkedIn API (opcional) para publicação
- Implementar webhooks para eventos em tempo real

## 📖 Documentações Oficiais

### Tailwind CSS com Vite
https://tailwindcss.com/docs/installation/using-vite

### Shadcn/ui Components
https://ui.shadcn.com/docs/components

### React + Vite
https://vitejs.dev/guide/

### Express.js
https://expressjs.com/

### GitHub API
https://docs.github.com/en/rest

### Gemini API
https://ai.google.dev/docs

## 🚀 Workflow de Desenvolvimento
1. **VERIFICAR se há modificações pendentes**
2. **SE HOUVER modificações:** Fazer commit + tag + changelog
3. **Consultar checklist.md** antes de cada etapa
4. **Implementar funcionalidade** seguindo padrões
5. **Testar localmente**
6. **Fazer commit com mensagem bilíngue**
7. **Atualizar checklist** marcando como concluído
8. **Criar release se necessário**
9. **Documentar mudanças no changelog**

## 🎯 Objetivos do Projeto
- Automatizar criação de posts do LinkedIn baseado em atividades do GitHub
- Usar IA para gerar conteúdo contextual e envolvente
- Interface moderna e tecnológica
- Experiência de usuário fluida e intuitiva
- Código limpo e bem documentado
- Arquitetura escalável e manutenível
