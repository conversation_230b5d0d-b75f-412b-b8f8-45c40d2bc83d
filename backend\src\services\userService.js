import User from '../models/User.js';

/**
 * Serviço para gerenciar usuários
 * Em produção, isso seria um repositório conectado ao banco de dados
 */
class UserService {
  constructor() {
    // Em produção, isso seria um banco de dados
    this.users = new Map();
    this.emailIndex = new Map(); // Índice para busca por email
    this.githubIdIndex = new Map(); // Índice para busca por GitHub ID

    // Inicializar com usuário de teste (COMENTADO PARA TESTES REAIS)
    // this.initializeTestUser();
  }

  // /**
  //  * Inicializar usuário de teste
  //  */
  // initializeTestUser() {
  //   const testUser = new User({
  //     id: '1',
  //     email: '<EMAIL>',
  //     password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.iK8O', // Secure@2024
  //     name: '<PERSON>',
  //     avatar: 'https://github.com/shadcn.png',
  //     emailVerified: true,
  //     isActive: true,
  //   });

  //   // Adicionar usuário diretamente sem validação (já é um hash válido)
  //   this.users.set(testUser.id, testUser);
  //   this.emailIndex.set(testUser.email.toLowerCase(), testUser.id);

  //   console.log(`✅ Usuário de teste inicializado: ${testUser.email}`);

  //   // Configurar token GitHub de teste para o usuário
  //   this.initializeTestGitHubToken();
  // }

  // /**
  //  * Inicializar token GitHub de teste
  //  */
  // initializeTestGitHubToken() {
  //   // Importar o service do GitHub
  //   import('../services/githubService.js').then(({ storeGitHubToken }) => {
  //     // Token GitHub de teste (substitua por um token real para testes)
  //     const testGitHubToken = process.env.GITHUB_TEST_TOKEN || 'gho_test_token_placeholder';

  //     if (testGitHubToken !== 'gho_test_token_placeholder') {
  //       storeGitHubToken('1', testGitHubToken);
  //       console.log('✅ Token GitHub de teste configurado para o usuário');
  //     } else {
  //       console.log('⚠️ Token GitHub de teste não configurado (defina GITHUB_TEST_TOKEN no .env)');
  //     }
  //   });
  // }

  /**
   * Criar novo usuário
   */
  async createUser(userData) {
    try {
      // Validar dados
      const validation = User.validate(userData);
      if (!validation.isValid) {
        throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
      }

      // Verificar se email já existe
      if (this.emailIndex.has(userData.email.toLowerCase())) {
        throw new Error('Email já está em uso');
      }

      // Verificar se GitHub ID já existe (se fornecido)
      if (userData.githubId && this.githubIdIndex.has(userData.githubId)) {
        throw new Error('Conta GitHub já está vinculada a outro usuário');
      }

      // Hash da senha se fornecida
      if (userData.password) {
        userData.password = await User.hashPassword(userData.password);
      }

      // Criar usuário
      const user = new User(userData);

      // Armazenar usuário
      this.users.set(user.id, user);
      this.emailIndex.set(user.email.toLowerCase(), user.id);

      if (user.githubId) {
        this.githubIdIndex.set(user.githubId, user.id);
      }

      console.log(`✅ Usuário criado: ${user.email} (ID: ${user.id})`);

      return user.toPublicJSON();
    } catch (error) {
      console.error('❌ Erro ao criar usuário:', error.message);
      throw error;
    }
  }

  /**
   * Buscar usuário por ID
   */
  getUserById(id) {
    const user = this.users.get(id);
    return user ? user.toPublicJSON() : null;
  }

  /**
   * Buscar usuário por email
   */
  getUserByEmail(email) {
    console.log('🔍 Buscando usuário por email:', email);
    console.log('🔍 Usuários disponíveis:', Array.from(this.users.keys()));
    
    for (const [id, user] of this.users) {
      console.log('🔍 Verificando usuário:', user.email);
      if (user.email === email) {
        console.log('✅ Usuário encontrado:', id);
        return user;
      }
    }
    
    console.log('❌ Usuário não encontrado');
    return null;
  }

  /**
   * Buscar usuário por GitHub ID
   */
  getUserByGitHubId(githubId) {
    const userId = this.githubIdIndex.get(githubId);
    if (!userId) return null;

    const user = this.users.get(userId);
    return user || null;
  }

  /**
   * Atualizar usuário
   */
  async updateUser(id, updateData) {
    try {
      const user = this.users.get(id);
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Verificar se email está sendo alterado
      if (updateData.email && updateData.email !== user.email) {
        if (this.emailIndex.has(updateData.email.toLowerCase())) {
          throw new Error('Email já está em uso');
        }

        // Remover índice antigo
        this.emailIndex.delete(user.email.toLowerCase());
        // Adicionar novo índice
        this.emailIndex.set(updateData.email.toLowerCase(), id);
      }

      // Verificar se GitHub ID está sendo alterado
      if (updateData.githubId && updateData.githubId !== user.githubId) {
        if (this.githubIdIndex.has(updateData.githubId)) {
          throw new Error('Conta GitHub já está vinculada a outro usuário');
        }

        // Remover índice antigo
        if (user.githubId) {
          this.githubIdIndex.delete(user.githubId);
        }
        // Adicionar novo índice
        this.githubIdIndex.set(updateData.githubId, id);
      }

      // Atualizar usuário
      user.update(updateData);

      console.log(`✅ Usuário atualizado: ${user.email} (ID: ${user.id})`);

      return user.toPublicJSON();
    } catch (error) {
      console.error('❌ Erro ao atualizar usuário:', error.message);
      throw error;
    }
  }

  /**
   * Vincular conta GitHub
   */
  async linkGitHubAccount(userId, githubData) {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Verificar se GitHub ID já está vinculado a outro usuário
      if (
        this.githubIdIndex.has(githubData.id) &&
        this.githubIdIndex.get(githubData.id) !== userId
      ) {
        throw new Error('Conta GitHub já está vinculada a outro usuário');
      }

      // Atualizar dados do GitHub
      const updateData = {
        githubId: githubData.id,
        githubUsername: githubData.login,
        githubToken: githubData.token,
      };

      // Se não tinha GitHub ID antes, adicionar ao índice
      if (!user.githubId) {
        this.githubIdIndex.set(githubData.id, userId);
      }

      user.update(updateData);

      console.log(
        `✅ Conta GitHub vinculada: ${user.email} -> ${githubData.login}`
      );

      return user.toPublicJSON();
    } catch (error) {
      console.error('❌ Erro ao vincular conta GitHub:', error.message);
      throw error;
    }
  }

  /**
   * Atualizar último login
   */
  updateLastLogin(id) {
    const user = this.users.get(id);
    if (user) {
      // Atualizar último login
      console.log('🔍 Atualizando último login...');
      user.updateLastLogin();
      console.log('✅ Último login atualizado');
    }
  }

  /**
   * Verificar credenciais de login
   */
  async verifyCredentials(email, password) {
    try {
      console.log('🔍 Verificando credenciais para:', email);
      
      const user = this.getUserByEmail(email);
      console.log('🔍 Usuário encontrado:', user ? 'SIM' : 'NÃO');
      
      if (!user) {
        console.log('❌ Usuário não encontrado');
        return { success: false, error: 'Usuário não encontrado' };
      }

      console.log('🔍 Verificando se usuário está ativo:', user.getIsActive());
      if (!user.getIsActive()) {
        console.log('❌ Usuário inativo');
        return { success: false, error: 'Conta desativada' };
      }

      console.log('🔍 Verificando senha...');
      const isValidPassword = await user.verifyPassword(password);
      console.log('🔍 Senha válida:', isValidPassword);
      
      if (!isValidPassword) {
        console.log('❌ Senha incorreta');
        return { success: false, error: 'Senha incorreta' };
      }

      // Atualizar último login
      user.updateLastLogin();
      console.log('✅ Credenciais válidas');

      return { 
        success: true, 
        user: user.toPublicJSON(),
      };
    } catch (error) {
      console.error('❌ Erro ao verificar credenciais:', error.message);
      console.error('❌ Stack trace:', error.stack);
      return { success: false, error: 'Erro interno do servidor' };
    }
  }

  /**
   * Listar todos os usuários (para admin)
   */
  getAllUsers() {
    return Array.from(this.users.values()).map(user => user.toPublicJSON());
  }

  /**
   * Deletar usuário
   */
  deleteUser(id) {
    try {
      const user = this.users.get(id);
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Remover índices
      this.emailIndex.delete(user.email.toLowerCase());
      if (user.githubId) {
        this.githubIdIndex.delete(user.githubId);
      }

      // Remover usuário
      this.users.delete(id);

      console.log(`✅ Usuário deletado: ${user.email} (ID: ${user.id})`);

      return true;
    } catch (error) {
      console.error('❌ Erro ao deletar usuário:', error.message);
      throw error;
    }
  }

  /**
   * Estatísticas do serviço
   */
  getStats() {
    return {
      totalUsers: this.users.size,
      activeUsers: Array.from(this.users.values()).filter(u => u.isActive())
        .length,
      verifiedEmails: Array.from(this.users.values()).filter(u =>
        u.isEmailVerified()
      ).length,
      linkedGitHub: Array.from(this.users.values()).filter(u => u.githubId)
        .length,
    };
  }
}

// Instância singleton do serviço
const userService = new UserService();

export default userService;
