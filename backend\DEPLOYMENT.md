# 🚀 Deploy para Produção - Code2Post

Guia completo para fazer deploy do Code2Post em produção com HTTPS automático.

## 📋 Pré-requisitos

### **1. Servidor Linux**
- Ubuntu 20.04+ ou Debian 10+
- 1GB RAM mínimo (2GB recomendado)
- 10GB espaço em disco
- Acesso root ou sudo

### **2. <PERSON><PERSON><PERSON>**
- Domínio apontando para o IP do servidor
- DNS configurado (A record)
- Tempo para propagação (pode levar até 24h)

### **3. Portas Disponíveis**
- **80** (HTTP - redirecionamento)
- **443** (HTTPS - principal)
- **3001** (API - opcional)

## 🔧 Configuração Inicial

### **1. Conectar ao Servidor**
```bash
ssh <EMAIL>
```

### **2. Instalar Node.js**
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar instalação
node --version
npm --version
```

### **3. Instalar Git**
```bash
sudo apt update
sudo apt install git
```

### **4. Clonar o Projeto**
```bash
git clone https://github.com/gabrielcamarate/Code2Post.git
cd Code2Post/backend
```

## ⚙️ Configuração do Ambiente

### **1. Configurar Variáveis de Ambiente**
```bash
# Copiar arquivo de exemplo
cp env.example .env

# Editar configurações
nano .env
```

### **2. Configurações Obrigatórias**
```env
# Ambiente
NODE_ENV=production

# Domínio (SUBSTITUA pelo seu domínio)
DOMAIN=seu-dominio.com
WWW_DOMAIN=www.seu-dominio.com

# Let's Encrypt
LETS_ENCRYPT_ENABLED=true
LETS_ENCRYPT_EMAIL=<EMAIL>
LETS_ENCRYPT_STAGING=false

# Frontend URL
FRONTEND_URL=https://seu-dominio.com

# JWT Secrets (GERE novos!)
JWT_SECRET=sua_chave_jwt_super_secreta_aqui
JWT_REFRESH_SECRET=sua_chave_refresh_super_secreta_aqui
SESSION_SECRET=sua_chave_sessao_super_secreta_aqui
```

### **3. Gerar Secrets Seguros**
```bash
# Gerar JWT secrets
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

## 🚀 Deploy Automático

### **1. Executar Script de Deploy**
```bash
# Tornar executável
chmod +x deploy-production.sh

# Executar deploy
./deploy-production.sh
```

### **2. O que o Script Faz**
- ✅ Verifica configurações
- ✅ Instala dependências
- ✅ Configura firewall
- ✅ Verifica DNS
- ✅ Inicia servidor
- ✅ Testa endpoints
- ✅ Configura Let's Encrypt

## 🔒 Configuração Manual (Alternativa)

### **1. Instalar Dependências**
```bash
npm ci --only=production
```

### **2. Criar Diretórios**
```bash
mkdir -p logs
mkdir -p greenlock.d
```

### **3. Configurar Firewall**
```bash
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3001/tcp
sudo ufw enable
```

### **4. Iniciar Servidor**
```bash
# Modo produção com Let's Encrypt
npm run start:prod

# Ou manualmente
NODE_ENV=production node src/server-production.js
```

## 📊 Monitoramento

### **1. Verificar Status**
```bash
# Verificar se está rodando
ps aux | grep node

# Verificar portas
sudo netstat -tlnp | grep :443
sudo netstat -tlnp | grep :80
```

### **2. Logs**
```bash
# Logs da aplicação
tail -f logs/app.log

# Logs do sistema
sudo journalctl -u code2post -f
```

### **3. Health Check**
```bash
# Testar endpoint
curl https://seu-dominio.com/health

# Verificar certificado SSL
openssl s_client -connect seu-dominio.com:443 -servername seu-dominio.com
```

## 🔄 Gerenciamento

### **1. Parar Servidor**
```bash
pkill -f "node.*server-production.js"
```

### **2. Reiniciar Servidor**
```bash
# Parar
pkill -f "node.*server-production.js"

# Iniciar
npm run start:prod
```

### **3. Atualizar Código**
```bash
# Pull das mudanças
git pull origin main

# Reinstalar dependências
npm ci --only=production

# Reiniciar
pkill -f "node.*server-production.js"
npm run start:prod
```

## 🛠️ Troubleshooting

### **Problema: Certificado não é gerado**
```bash
# Verificar logs do Let's Encrypt
tail -f greenlock.d/logs/letsencrypt.log

# Verificar se o domínio aponta para o servidor
dig seu-dominio.com

# Verificar se as portas estão abertas
sudo ufw status
```

### **Problema: Servidor não inicia**
```bash
# Verificar configurações
npm run code:check

# Verificar variáveis de ambiente
echo $NODE_ENV
echo $DOMAIN

# Verificar permissões
ls -la logs/
ls -la greenlock.d/
```

### **Problema: Redirecionamento não funciona**
```bash
# Verificar se HTTP está rodando
curl -I http://seu-dominio.com

# Verificar configuração do app
grep -r "redirectToHTTPS" src/
```

## 📈 Performance

### **1. Otimizações Recomendadas**
- Usar PM2 para gerenciamento de processos
- Configurar Nginx como proxy reverso
- Implementar cache Redis
- Configurar CDN para assets

### **2. Monitoramento**
- Configurar New Relic ou DataDog
- Implementar logs estruturados
- Configurar alertas de uptime

## 🔐 Segurança

### **1. Checklist de Segurança**
- [ ] Firewall configurado
- [ ] Secrets gerados aleatoriamente
- [ ] HTTPS forçado
- [ ] Headers de segurança ativos
- [ ] Rate limiting configurado
- [ ] Logs de segurança ativos

### **2. Manutenção**
- Renovar certificados automaticamente
- Atualizar dependências regularmente
- Monitorar logs de segurança
- Fazer backup das configurações

## 📞 Suporte

Se encontrar problemas:

1. **Verificar logs:** `tail -f logs/app.log`
2. **Verificar configurações:** `cat .env`
3. **Testar conectividade:** `curl -I https://seu-dominio.com`
4. **Verificar DNS:** `dig seu-dominio.com`

---

**🎯 Próximos Passos:**
- Configurar CI/CD
- Implementar monitoramento
- Configurar backup automático
- Otimizar performance 