import bcrypt from 'bcryptjs';

/**
 * Modelo de Usuário para o Code2Post
 * Em produção, isso seria um schema de banco de dados (MongoDB, PostgreSQL, etc.)
 */
class User {
    constructor(data) {
        this.id = data.id || this.generateId();
        this.email = data.email;
        this.password = data.password; // Hash da senha
        this.name = data.name;
        this.avatar = data.avatar || null;
        this.githubId = data.githubId || null;
        this.githubUsername = data.githubUsername || null;
        this.githubToken = data.githubToken || null;
        this.isActive = data.isActive !== false; // Por padrão, usuário ativo
        this.emailVerified = data.emailVerified || false;
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
        this.lastLoginAt = data.lastLoginAt || null;
        this.preferences = data.preferences || {
            theme: 'dark',
            language: 'pt-BR',
            notifications: {
                email: true,
                push: false,
            },
        };
    }

    /**
     * Gerar ID único para o usuário
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Validar dados do usuário
     */
    static validate(data) {
        const errors = [];

        // Validar email
        if (!data.email || !this.isValidEmail(data.email)) {
            errors.push('Email inválido');
        }

        // Validar nome
        if (!data.name || data.name.trim().length < 3) {
            errors.push('Nome deve ter pelo menos 3 caracteres');
        }

        // Validar senha (apenas se for registro)
        if (data.password && !this.isValidPassword(data.password)) {
            errors.push(
                'Senha deve ter pelo menos 8 caracteres, 1 maiúscula, 1 minúscula, 1 número e 1 símbolo'
            );
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    /**
     * Validar formato de email
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validar força da senha
     */
    static isValidPassword(password) {
        // Mínimo 8 caracteres, 1 maiúscula, 1 minúscula, 1 número, 1 símbolo
        const passwordRegex =
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }

    /**
     * Hash da senha
     */
    static async hashPassword(password) {
        const saltRounds = 12; // Aumentado para maior segurança
        return await bcrypt.hash(password, saltRounds);
    }

    /**
     * Verificar senha
     */
    async verifyPassword(password) {
        return await bcrypt.compare(password, this.password);
    }

    /**
     * Atualizar dados do usuário
     */
    update(data) {
        if (data.name) this.name = data.name;
        if (data.avatar) this.avatar = data.avatar;
        if (data.githubId) this.githubId = data.githubId;
        if (data.githubUsername) this.githubUsername = data.githubUsername;
        if (data.githubToken) this.githubToken = data.githubToken;
        if (data.preferences)
            this.preferences = { ...this.preferences, ...data.preferences };

        this.updatedAt = new Date();
    }

    /**
     * Atualizar último login
     */
    updateLastLogin() {
        this.lastLoginAt = new Date();
    }

    /**
     * Verificar se email está verificado
     */
    isEmailVerified() {
        return this.emailVerified;
    }

    /**
     * Marcar email como verificado
     */
    verifyEmail() {
        this.emailVerified = true;
        this.updatedAt = new Date();
    }

    /**
     * Verificar se usuário está ativo
     */
    getIsActive() {
        return this.isActive;
    }

    /**
     * Desativar usuário
     */
    deactivate() {
        this.isActive = false;
        this.updatedAt = new Date();
    }

    /**
     * Ativar usuário
     */
    activate() {
        this.isActive = true;
        this.updatedAt = new Date();
    }

    /**
     * Retornar dados públicos do usuário (sem senha)
     */
    toPublicJSON() {
        return {
            id: this.id,
            email: this.email,
            name: this.name,
            avatar: this.avatar,
            githubId: this.githubId,
            githubUsername: this.githubUsername,
            isActive: this.isActive,
            emailVerified: this.emailVerified,
            createdAt: this.createdAt,
            lastLoginAt: this.lastLoginAt,
            preferences: this.preferences,
        };
    }

    /**
     * Retornar dados completos do usuário (para uso interno)
     */
    toJSON() {
        return {
            id: this.id,
            email: this.email,
            password: this.password,
            name: this.name,
            avatar: this.avatar,
            githubId: this.githubId,
            githubUsername: this.githubUsername,
            githubToken: this.githubToken,
            isActive: this.isActive,
            emailVerified: this.emailVerified,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            lastLoginAt: this.lastLoginAt,
            preferences: this.preferences,
        };
    }
}

export default User;
