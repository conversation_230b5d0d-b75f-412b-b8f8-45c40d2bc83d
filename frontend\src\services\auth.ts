import api from './api';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
}

class AuthService {
  // Login com credenciais
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  }

  // Login com GitHub OAuth
  async loginWithGitHub(code: string): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/github', { code });
    return response.data;
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Mesmo se der erro, limpar dados locais
      console.error('Erro no logout:', error);
    } finally {
      // Sempre limpar dados locais
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    }
  }

  // Verificar se o token é válido
  async verifyToken(): Promise<User> {
    const response = await api.get<User>('/auth/verify');
    return response.data;
  }

  // Refresh token
  async refreshToken(): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/refresh');
    return response.data;
  }

  // Salvar token no localStorage
  saveToken(token: string): void {
    localStorage.setItem('authToken', token);
  }

  // Obter token do localStorage
  getToken(): string | null {
    return localStorage.getItem('authToken');
  }

  // Salvar dados do usuário
  saveUser(user: User): void {
    localStorage.setItem('user', JSON.stringify(user));
  }

  // Obter dados do usuário
  getUser(): User | null {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }

  // Verificar se está autenticado
  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export default new AuthService(); 