import { Link } from 'react-router-dom';
import { ArrowLef<PERSON>, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PolicyNavigationProps {
  currentPage: 'privacy' | 'terms' | 'cookies';
}

export function PolicyNavigation({ currentPage }: PolicyNavigationProps) {
  const pages = [
    { key: 'privacy', to: '/privacy-policy', label: 'Política de Privacidade' },
    { key: 'terms', to: '/terms-of-service', label: 'Termos de Serviço' },
    { key: 'cookies', to: '/cookie-policy', label: 'Política de Cookies' }
  ];

  const currentIndex = pages.findIndex(page => page.key === currentPage);
  const prevPage = currentIndex > 0 ? pages[currentIndex - 1] : null;
  const nextPage = currentIndex < pages.length - 1 ? pages[currentIndex + 1] : null;

  return (
    <div className="flex justify-between items-center">
      <div>
        {prevPage ? (
          <Link to={prevPage.to}>
            <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
              <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              {prevPage.label}
            </Button>
          </Link>
        ) : (
          <Link to="/">
            <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
              <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              Voltar ao Início
            </Button>
          </Link>
        )}
      </div>

      <div>
        {nextPage && (
          <Link to={nextPage.to}>
            <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
              {nextPage.label}
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        )}
      </div>
    </div>
  );
}
