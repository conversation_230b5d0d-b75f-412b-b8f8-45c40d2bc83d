import api from './api';
import type { GitHubRepository, GitHubCommit, GitHubUser } from '@/types';

class GitHubService {
  // Obter token GitHub do localStorage
  private getGitHubToken(): string | null {
    return localStorage.getItem('githubToken');
  }

  // Buscar repositórios do usuário
  async getUserRepositories(): Promise<GitHubRepository[]> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<{ repositories: GitHubRepository[] }>('/api/github/repositories', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data.repositories || response.data;
  }



  // Buscar informações do usuário
  async getUserInfo(): Promise<GitHubUser> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<GitHubUser>('/api/github/permissions', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data;
  }

  // Buscar commits recentes de todos os repositórios
  async getRecentCommits(limit: number = 20): Promise<GitHubCommit[]> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<GitHubCommit[]>('/api/github/commits', {
      headers: {
        'x-github-token': githubToken
      },
      params: { limit }
    });
    
    return response.data;
  }

  // Configurar webhook para um repositório
  async setupWebhook(owner: string, repo: string): Promise<void> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    await api.post(`/api/github/repositories/${owner}/${repo}/webhook`, {}, {
      headers: {
        'x-github-token': githubToken
      }
    });
  }

  // Remover webhook de um repositório
  async removeWebhook(owner: string, repo: string): Promise<void> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    await api.delete(`/api/github/repositories/${owner}/${repo}/webhook`, {
      headers: {
        'x-github-token': githubToken
      }
    });
  }

  // Buscar estatísticas do usuário
  async getUserStats(): Promise<{
    totalRepos: number;
    totalCommits: number;
    totalStars: number;
    languages: Record<string, number>;
  }> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get('/api/github/stats', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data;
  }

  // Buscar linguagens de um repositório específico
  async getRepositoryLanguages(owner: string, repo: string): Promise<{
    name: string;
    bytes: number;
    percentage: string;
  }[]> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/languages`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data.languages;
  }

  // Buscar total de commits de um repositório específico
  async getRepositoryCommitsCount(owner: string, repo: string): Promise<number> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/commits-count`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data.totalCommits;
  }

  // Buscar branches de um repositório específico
  async getRepositoryBranches(owner: string, repo: string): Promise<{
    name: string;
    commit: {
      sha: string;
      url: string;
    };
    protected: boolean;
  }[]> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/branches`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data.branches;
  }

  // Buscar contagem de branches de um repositório específico
  async getRepositoryBranchesCount(owner: string, repo: string): Promise<number> {
    const branches = await this.getRepositoryBranches(owner, repo);
    return branches.length;
  }

  // Buscar commits de um repositório específico com paginação
  async getRepositoryCommits(owner: string, repo: string, page: number = 1, perPage: number = 10): Promise<{
    commits: GitHubCommit[];
    totalPages: number;
    currentPage: number;
    totalCommits: number;
  }> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/commits`, {
      headers: {
        'x-github-token': githubToken
      },
      params: {
        page,
        per_page: perPage
      }
    });

    return response.data;
  }

  // Buscar insights de um repositório específico
  async getRepositoryInsights(owner: string, repo: string): Promise<{
    size: number;
    lastPush: string;
    views?: number;
    clones?: number;
    defaultBranch: string;
    openIssues: number;
    watchers: number;
  }> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/insights`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data;
  }

  // Verificar se o usuário tem token GitHub configurado
  hasGitHubToken(): boolean {
    return !!this.getGitHubToken();
  }
}

export default new GitHubService(); 