// import { Octokit } from '@octokit/rest'; // 🎭 REMOVIDO - USANDO DADOS FICTÍCIOS

// Armazenamento temporário de tokens GitHub por usuário
// Em produção, isso deveria estar em um banco de dados
const userGitHubTokens = new Map();

/**
 * Armazena o token GitHub de um usuário
 * @param {string} userId - ID do usuário
 * @param {string} token - Token GitHub
 */
function storeGitHubToken(userId, token) {
  console.log(`💾 Armazenando token GitHub para userId: ${userId}`);
  userGitHubTokens.set(userId, token);
  console.log(`✅ Token armazenado. Total de tokens: ${userGitHubTokens.size}`);
}

/**
 * Obtém o token GitHub de um usuário
 * @param {string} userId - ID do usuário
 * @returns {string|null} Token GitHub ou null se não encontrado
 */
function getGitHubToken(userId) {
  const token = userGitHubTokens.get(userId) || null;
  console.log(
    `🔍 Buscando token GitHub para userId: ${userId}, encontrado: ${token ? 'SIM' : 'NÃO'}`
  );
  return token;
}

/**
 * Cria uma instância do Octokit para um usuário
 * @param {string} userId - ID do usuário
 * @returns {Octokit|null} Instância do Octokit ou null se não houver token
 */
function createOctokit(userId) {
  const token = getGitHubToken(userId);
  if (!token) {
    return null;
  }

  return new Octokit({
    auth: token,
    userAgent: 'Code2Post-App',
  });
}

/**
 * Busca repositórios de um usuário
 * @param {string} userId - ID do usuário
 * @returns {Promise<Array>} Lista de repositórios
 */
async function getRepositories(userId) {
  try {
    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log(`🎭 Retornando repositórios fictícios para userId: ${userId}`);

    // 🎭 REPOSITÓRIOS FICTÍCIOS
    return [
      {
        id: 1,
        name: 'code2post-frontend',
        full_name: 'gabrielcamarate/code2post-frontend',
        description: 'Frontend moderno em React + TypeScript para automação de posts do LinkedIn',
        private: false,
        html_url: 'https://github.com/gabrielcamarate/code2post-frontend',
        updated_at: '2024-01-30T10:30:00Z',
        language: 'TypeScript',
        stargazers_count: 42,
        forks_count: 8
      },
      {
        id: 2,
        name: 'ai-content-generator',
        full_name: 'gabrielcamarate/ai-content-generator',
        description: 'Sistema de geração de conteúdo usando IA para redes sociais',
        private: false,
        html_url: 'https://github.com/gabrielcamarate/ai-content-generator',
        updated_at: '2024-01-29T15:45:00Z',
        language: 'Python',
        stargazers_count: 89,
        forks_count: 23
      },
      {
        id: 3,
        name: 'react-dashboard-template',
        full_name: 'gabrielcamarate/react-dashboard-template',
        description: 'Template moderno de dashboard com React, Tailwind CSS e componentes reutilizáveis',
        private: true,
        html_url: 'https://github.com/gabrielcamarate/react-dashboard-template',
        updated_at: '2024-01-28T09:20:00Z',
        language: 'JavaScript',
        stargazers_count: 15,
        forks_count: 3
      }
    ];
  } catch (error) {
    console.error('Erro ao buscar repositórios:', error);
    throw new Error('Falha ao buscar repositórios do GitHub');
  }
}

/**
 * Busca commits de um repositório
 * @param {string} userId - ID do usuário
 * @param {string} repoName - Nome do repositório
 * @param {string} branch - Branch (padrão: main)
 * @param {number} limit - Limite de commits (padrão: 10)
 * @returns {Promise<Array>} Lista de commits
 */
async function getCommits(userId, repoName, branch = 'main', limit = 10) {
  try {
    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log(`🎭 Retornando commits fictícios para ${repoName} (userId: ${userId})`);

    // 🎭 COMMITS FICTÍCIOS
    const fakeCommits = [
      {
        sha: 'abc123def456',
        commit: {
          message: `feat: implement new dashboard components for ${repoName}`,
          author: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-30T10:30:00Z'
          },
          committer: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-30T10:30:00Z'
          }
        },
        html_url: `https://github.com/gabrielcamarate/${repoName}/commit/abc123def456`,
        parents: [{ sha: 'parent123' }]
      },
      {
        sha: 'def456ghi789',
        commit: {
          message: 'fix: resolve authentication issues in login flow',
          author: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-29T15:45:00Z'
          },
          committer: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-29T15:45:00Z'
          }
        },
        html_url: `https://github.com/gabrielcamarate/${repoName}/commit/def456ghi789`,
        parents: [{ sha: 'parent456' }]
      }
    ];

    return fakeCommits.slice(0, limit).map(commit => ({
      sha: commit.sha,
      commit: {
        message: commit.commit.message,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          date: commit.commit.author.date,
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          date: commit.commit.committer.date,
        },
      },
      // author e committer já estão dentro de commit
      html_url: commit.html_url,
      parents: commit.parents,
    }));
  } catch (error) {
    console.error('Erro ao buscar commits:', error);
    throw new Error('Falha ao buscar commits do GitHub');
  }
}

/**
 * Busca informações de um repositório específico
 * @param {string} userId - ID do usuário
 * @param {string} repoName - Nome do repositório
 * @returns {Promise<Object>} Informações do repositório
 */
async function getRepository(userId, repoName) {
  try {
    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log(`🎭 Retornando repositório fictício: ${repoName} (userId: ${userId})`);

    // 🎭 REPOSITÓRIO FICTÍCIO
    return {
      id: 1,
      name: repoName,
      full_name: `gabrielcamarate/${repoName}`,
      description: `Repositório fictício para ${repoName}`,
      private: false,
      html_url: `https://github.com/gabrielcamarate/${repoName}`,
      updated_at: '2024-01-30T10:30:00Z',
      language: 'JavaScript',
      stargazers_count: response.data.stargazers_count,
      forks_count: response.data.forks_count,
      open_issues_count: response.data.open_issues_count,
      default_branch: response.data.default_branch,
    };
  } catch (error) {
    console.error('Erro ao buscar repositório:', error);
    throw new Error('Falha ao buscar informações do repositório');
  }
}

/**
 * Verifica se um token GitHub é válido
 * @param {string} token - Token GitHub
 * @returns {Promise<boolean>} True se válido, false caso contrário
 */
async function validateGitHubToken(token) {
  try {
    // 🎭 DADOS FICTÍCIOS - NÃO USA OCTOKIT
    console.log('🎭 Validando token fictício...');

    // 🎭 SIMULAR VALIDAÇÃO SEMPRE VÁLIDA
    const response = {
      data: {
        id: 12345678,
        login: 'gabrielcamarate',
        name: 'Gabriel Camarate',
        email: '<EMAIL>'
      }
    };
    return !!response.data;
  } catch (error) {
    console.error('Token GitHub inválido:', error);
    return false;
  }
}

export {
  storeGitHubToken,
  getGitHubToken,
  getRepositories,
  getCommits,
  getRepository,
  validateGitHubToken,
};
