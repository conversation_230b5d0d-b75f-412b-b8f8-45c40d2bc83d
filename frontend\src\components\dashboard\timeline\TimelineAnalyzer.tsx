import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  GitBranch, 
  Calendar, 
  TrendingUp, 
  Clock, 
  Zap, 
  Target,
  BarChart3,
  GitCommit,
  Users,
  Star
} from 'lucide-react';

interface CommitData {
  date: string;
  count: number;
  message: string;
  author: string;
}

interface ProjectAnalysis {
  totalCommits: number;
  timeSpan: {
    start: string;
    end: string;
    duration: string;
  };
  commitFrequency: 'high' | 'medium' | 'low';
  projectType: 'finished' | 'ongoing' | 'abandoned';
  suggestedMode: 'timeline' | 'traditional';
  keyMilestones: string[];
  topLanguages: { name: string; percentage: number; color: string }[];
  activityPattern: CommitData[];
}

interface TimelineAnalyzerProps {
  repositoryName: string;
  onAnalysisComplete: (analysis: ProjectAnalysis) => void;
}

export default function TimelineAnalyzer({ repositoryName, onAnalysisComplete }: TimelineAnalyzerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<ProjectAnalysis | null>(null);
  const [progress, setProgress] = useState(0);

  // Dados fake para demonstração - em produção virá da API
  const mockAnalysis: ProjectAnalysis = {
    totalCommits: 87,
    timeSpan: {
      start: '2024-01-15',
      end: '2024-12-20',
      duration: '11 meses'
    },
    commitFrequency: 'high',
    projectType: 'finished',
    suggestedMode: 'timeline',
    keyMilestones: [
      'Configuração inicial do projeto',
      'Implementação do backend',
      'Desenvolvimento do frontend',
      'Integração com APIs',
      'Deploy em produção'
    ],
    topLanguages: [
      { name: 'TypeScript', percentage: 45, color: 'bg-blue-500' },
      { name: 'JavaScript', percentage: 30, color: 'bg-yellow-500' },
      { name: 'CSS', percentage: 15, color: 'bg-purple-500' },
      { name: 'HTML', percentage: 10, color: 'bg-orange-500' }
    ],
    activityPattern: [
      { date: '2024-01', count: 12, message: 'Setup inicial', author: 'Gabriel' },
      { date: '2024-02', count: 8, message: 'Backend development', author: 'Gabriel' },
      { date: '2024-03', count: 15, message: 'Frontend implementation', author: 'Gabriel' },
      { date: '2024-04', count: 10, message: 'API integrations', author: 'Gabriel' },
      { date: '2024-05', count: 18, message: 'UI/UX improvements', author: 'Gabriel' },
      { date: '2024-06', count: 14, message: 'Testing and fixes', author: 'Gabriel' },
      { date: '2024-07', count: 10, message: 'Production deployment', author: 'Gabriel' }
    ]
  };

  const startAnalysis = async () => {
    setIsAnalyzing(true);
    setProgress(0);

    // Simular análise com progresso
    const steps = [
      'Coletando histórico de commits...',
      'Analisando padrões temporais...',
      'Identificando marcos importantes...',
      'Categorizando tipos de commit...',
      'Gerando recomendações...'
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setProgress((i + 1) * 20);
    }

    setAnalysis(mockAnalysis);
    setIsAnalyzing(false);
    onAnalysisComplete(mockAnalysis);
  };

  const getProjectTypeColor = (type: string) => {
    switch (type) {
      case 'finished': return 'bg-green-500/10 text-green-400 border-green-500/30';
      case 'ongoing': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'abandoned': return 'bg-red-500/10 text-red-400 border-red-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const getModeRecommendation = (mode: string) => {
    return mode === 'timeline' 
      ? { 
          icon: Clock, 
          title: 'Timeline Retroativa', 
          description: 'Ideal para contar a história completa do desenvolvimento',
          color: 'text-purple-400'
        }
      : { 
          icon: Zap, 
          title: 'Modo Tradicional', 
          description: 'Perfeito para projetos em desenvolvimento ativo',
          color: 'text-blue-400'
        };
  };

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
          Análise do Repositório
        </CardTitle>
        <CardDescription className="text-slate-300">
          Analisando <span className="text-blue-400 font-medium">{repositoryName}</span> para determinar a melhor estratégia de timeline
        </CardDescription>
      </CardHeader>

      <CardContent>
        {!analysis && !isAnalyzing && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <GitBranch className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Pronto para Análise</h3>
            <p className="text-slate-400 mb-6">
              Vamos analisar o histórico de commits e sugerir a melhor estratégia para seus posts
            </p>
            <Button 
              onClick={startAnalysis}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Iniciar Análise
            </Button>
          </div>
        )}

        {isAnalyzing && (
          <div className="py-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Analisando Repositório...</h3>
              <p className="text-slate-400 mb-4">
                Processando histórico de commits e identificando padrões
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-slate-300">Progresso da análise</span>
                <span className="text-blue-400">{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </div>
        )}

        {analysis && (
          <div className="space-y-6">
            {/* Resumo Geral */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                <div className="flex items-center justify-between mb-2">
                  <GitCommit className="w-5 h-5 text-blue-400" />
                  <span className="text-2xl font-bold text-white">{analysis.totalCommits}</span>
                </div>
                <p className="text-sm text-slate-300">Total de Commits</p>
              </div>

              <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                <div className="flex items-center justify-between mb-2">
                  <Calendar className="w-5 h-5 text-green-400" />
                  <span className="text-2xl font-bold text-white">{analysis.timeSpan.duration}</span>
                </div>
                <p className="text-sm text-slate-300">Período de Desenvolvimento</p>
              </div>

              <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                <div className="flex items-center justify-between mb-2">
                  <TrendingUp className="w-5 h-5 text-purple-400" />
                  <Badge className={getProjectTypeColor(analysis.projectType)}>
                    {analysis.projectType === 'finished' ? 'Finalizado' : 
                     analysis.projectType === 'ongoing' ? 'Em Andamento' : 'Abandonado'}
                  </Badge>
                </div>
                <p className="text-sm text-slate-300">Status do Projeto</p>
              </div>
            </div>

            {/* Recomendação de Modo */}
            <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-6 border border-purple-500/20">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                  {(() => {
                    const rec = getModeRecommendation(analysis.suggestedMode);
                    const Icon = rec.icon;
                    return <Icon className="w-6 h-6 text-white" />;
                  })()}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-1">
                    Recomendação: {getModeRecommendation(analysis.suggestedMode).title}
                  </h3>
                  <p className="text-slate-300 mb-3">
                    {getModeRecommendation(analysis.suggestedMode).description}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-green-400">
                      {analysis.suggestedMode === 'timeline' 
                        ? `Potencial para ${Math.ceil(analysis.totalCommits / 3)} posts únicos`
                        : 'Ideal para posts em tempo real'
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Linguagens */}
            <div>
              <h4 className="text-white font-medium mb-3 flex items-center">
                <Star className="w-4 h-4 mr-2 text-yellow-400" />
                Linguagens Principais
              </h4>
              <div className="space-y-2">
                {analysis.topLanguages.map((lang, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${lang.color}`}></div>
                    <span className="text-slate-300 text-sm flex-1">{lang.name}</span>
                    <span className="text-slate-400 text-sm">{lang.percentage}%</span>
                    <div className="w-20 bg-slate-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${lang.color}`}
                        style={{ width: `${lang.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Marcos Importantes */}
            <div>
              <h4 className="text-white font-medium mb-3 flex items-center">
                <Users className="w-4 h-4 mr-2 text-blue-400" />
                Marcos Identificados
              </h4>
              <div className="space-y-2">
                {analysis.keyMilestones.map((milestone, index) => (
                  <div key={index} className="flex items-center space-x-3 p-2 bg-slate-700/20 rounded">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{milestone}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Botão de Ação */}
            <div className="pt-4 border-t border-slate-600/30">
              <Button 
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                onClick={() => {
                  // Próximo passo: abrir TimelinePlanner
                  console.log('Configurar Timeline com:', analysis);
                }}
              >
                <Clock className="w-4 h-4 mr-2" />
                Configurar Timeline Retroativa
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
