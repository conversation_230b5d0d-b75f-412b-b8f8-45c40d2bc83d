import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'sonner';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { LoadingScreen } from '@/components/ui/loading';
import Login from '@/pages/Login';
import Register from '@/pages/Register';
import TermsOfService from '@/pages/TermsOfService';
import PrivacyPolicy from '@/pages/PrivacyPolicy';
import CookiePolicy from '@/pages/CookiePolicy';
import Dashboard from '@/pages/Dashboard';
import Timeline from '@/pages/Timeline';
import Repositories from '@/pages/Repositories';
import Posts from '@/pages/Posts';
import Analytics from '@/pages/Analytics';
import Settings from '@/pages/Settings';
import Scheduling from '@/pages/Scheduling';
import SpinnerTest from '@/pages/SpinnerTest';
import GitHubCallback from '@/pages/GitHubCallback';
import LandingPage from '@/pages/LandingPage';

function AppRoutes() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Routes>
      <Route
        path="/"
        element={<LandingPage />}
      />
      <Route
        path="/login"
        element={isAuthenticated ? <Navigate to="/dashboard" /> : <Login />}
      />
      <Route 
        path="/register" 
        element={isAuthenticated ? <Navigate to="/dashboard" /> : <Register />} 
      />
      <Route 
        path="/terms-of-service" 
        element={<TermsOfService />} 
      />
      <Route
        path="/privacy-policy"
        element={<PrivacyPolicy />}
      />
      <Route
        path="/cookie-policy"
        element={<CookiePolicy />}
      />
      <Route
        path="/dashboard"
        element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" />}
      />
      <Route
        path="/timeline"
        element={isAuthenticated ? <Timeline /> : <Navigate to="/login" />}
      />
      <Route
        path="/repositories"
        element={isAuthenticated ? <Repositories /> : <Navigate to="/login" />}
      />
      <Route
        path="/posts"
        element={isAuthenticated ? <Posts /> : <Navigate to="/login" />}
      />
      <Route
        path="/analytics"
        element={isAuthenticated ? <Analytics /> : <Navigate to="/login" />}
      />
      <Route
        path="/scheduling"
        element={isAuthenticated ? <Scheduling /> : <Navigate to="/login" />}
      />
      <Route
        path="/settings"
        element={isAuthenticated ? <Settings /> : <Navigate to="/login" />}
      />
      <Route
        path="/spinner-test"
        element={<SpinnerTest />}
      />
      <Route 
        path="/auth/github/callback" 
        element={<GitHubCallback />} 
      />
      <Route
        path="*"
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/"} />}
      />
    </Routes>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppRoutes />
        <Toaster />
      </Router>
    </AuthProvider>
  );
}

export default App;
