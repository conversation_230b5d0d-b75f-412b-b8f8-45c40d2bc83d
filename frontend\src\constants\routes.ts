// Rotas da aplicação
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PRIVACY_POLICY: '/privacy-policy',
  TERMS_OF_SERVICE: '/terms-of-service',
  COOKIE_POLICY: '/cookie-policy',
  GITHUB_CALLBACK: '/auth/github/callback',
  SPINNER_TEST: '/spinner-test'
} as const;

// URLs da API
export const API_URLS = {
  BASE: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    GITHUB: '/api/auth/github',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me'
  },
  GITHUB: {
    REPOS: '/api/github/repos',
    COMMITS: '/api/github/commits',
    USER: '/api/github/user'
  },
  POSTS: {
    GENERATE: '/api/posts/generate',
    LIST: '/api/posts',
    PUBLISH: '/api/posts/publish'
  }
} as const;

// URLs externas
export const EXTERNAL_URLS = {
  GITHUB: 'https://github.com',
  LINKEDIN: 'https://linkedin.com',
  SUPPORT_EMAIL: 'mailto:<EMAIL>',
  PRIVACY_EMAIL: 'mailto:<EMAIL>'
} as const;
