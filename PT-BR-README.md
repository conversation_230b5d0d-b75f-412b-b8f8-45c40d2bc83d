<div align="center">
  <img src="https://raw.githubusercontent.com/gabrielcamarate/Code2Post/main/frontend/public/png/logo-4x.png" alt="Code2Post" width="128" height="128">

  # 🚀 Code2Post

  **Automatize e compartilhe seu progresso de desenvolvimento no LinkedIn, diretamente do seu GitHub, alimentado por IA.**

  [🇺🇸 English Version](README.md) | [🇧🇷 Versão em Português](PT-BR-README.md)
</div>

![GitHub language count](https://img.shields.io/github/languages/count/gabrielcamarate/Code2Post?style=for-the-badge)
![Version](https://img.shields.io/badge/version-v1.6.0-blue?style=for-the-badge)
![Status](https://img.shields.io/badge/status-development-green?style=for-the-badge)

<br>

<p align="center">
  <!-- Adicione a imagem de preview do seu projeto abaixo -->
  <img src="images/preview.png" alt="Preview do Code2Post" width="800"/>
</p>

> ### 🔗 **[Demo Online - www.code2post.com](https://www.code2post.com)**

<br>

## 📋 Índice

* [Sobre](#-sobre)
* [Funcionalidades](#-funcionalidades)
* [Tecnologias Utilizadas](#-tecnologias-utilizadas)
* [Arquitetura](#-arquitetura)
* [Recursos de Segurança](#-recursos-de-segurança)
* [Como Executar](#-como-executar)
* [Testes da API](#-testes-da-api)
* [Roadmap de Desenvolvimento](#-roadmap-de-desenvolvimento)
* [Licença](#-licença)
* [Contato](#-contato)

<br>

## 📖 Sobre

**Code2Post** é uma aplicação web que conecta seu GitHub ao LinkedIn, gerando automaticamente sugestões de posts sobre o progresso do seu projeto, usando inteligência artificial para criar textos contextuais e envolventes.

**Status Atual:** Sistema completo de autenticação implementado com AuthContext, validação interativa de senha e gerenciamento adequado de sessão. Backend e frontend totalmente integrados com UI moderna e recursos de segurança.

<br>

## ✨ Funcionalidades

### ✅ **Implementado (v1.2.3)**
* **🔐 Sistema Completo de Autenticação JWT**
  - Hash seguro de senhas com bcryptjs
  - Access tokens (15min) + Refresh tokens (7 dias)
  - Blacklist de tokens para logout seguro
  - Validação robusta de dados com express-validator
  - Rate limiting para proteção contra ataques de força bruta
  - Headers de segurança com middleware helmet
  - Política de senha forte (8+ caracteres, maiúscula, minúscula, número, símbolo)

* **🛡️ Recursos Avançados de Segurança**
  - **Proteção CSRF:** Proteção completa contra ataques Cross-Site Request Forgery
  - **Gerenciamento de Sessões:** Manipulação segura de sessões com express-session
  - **Rate Limiting:** Proteção contra ataques de força bruta (5 tentativas por 15 minutos)
  - **Headers de Segurança:** Proteção contra XSS, CSRF, clickjacking com helmet
  - **Prevenção de MIME Sniffing:** Previne ataques de sniffing de tipo de conteúdo
  - **HTTP Strict Transport Security (HSTS):** Força conexões HTTPS
  - **Content Security Policy (CSP):** Restringe carregamento de recursos a fontes confiáveis
  - **Validação de Dados Pessoais:** Previne senhas contendo informações pessoais

* **🧪 Testes e Validação**
  - Endpoints abrangentes de teste da API
  - Mensagens de erro de validação em português
  - Testes de segurança com comandos curl
  - Health check e monitoramento de status

### 🚧 **Em Desenvolvimento**
* **Integração GitHub:** Monitoramento de repositórios e detecção de eventos
* **Geração de Posts com IA:** Integração com API Gemini para posts contextuais
* **Publicação no LinkedIn:** Publicação direta de posts (opcional)
* **Dashboard Frontend:** Interface completa do usuário
* **Suporte Multi-usuário:** Gerenciamento de usuários e perfis

<br>

## 💻 Tecnologias Utilizadas

### **Backend (Implementado)**
- **Node.js** com Express.js
- **JWT** para autenticação com refresh tokens
- **bcryptjs** para hash de senhas
- **express-validator** para validação de dados
- **helmet** para headers de segurança
- **express-rate-limit** para rate limiting
- **express-session** para gerenciamento de sessões
- **csrf** para proteção CSRF
- **CORS** para requisições cross-origin

### **Frontend (Em Desenvolvimento)**
- **React** com TypeScript
- **Vite** como build tool
- **Tailwind CSS** para estilização
- **shadcn/ui** para componentes
- **React Router DOM** para navegação
- **Axios** para comunicação com API

### **APIs (Planejado)**
- **GitHub API** para dados de repositórios
- **Gemini API** para geração de posts com IA
- **LinkedIn API** para publicação (opcional)

<br>

## 🏗️ Arquitetura

```
Code2Post/
├── backend/                 # API Node.js/Express
│   ├── src/
│   │   ├── routes/         # Rotas da API (auth, etc.)
│   │   ├── middleware/     # Auth JWT, validação
│   │   ├── controllers/    # Lógica de negócio
│   │   └── app.js         # Aplicação principal
│   ├── package.json
│   └── .env
├── frontend/               # Aplicação React
│   ├── src/
│   │   ├── components/    # Componentes UI
│   │   ├── pages/        # Páginas da aplicação
│   │   ├── services/     # Serviços da API
│   │   └── contexts/     # Contextos React
│   └── package.json
├── README.md
├── PT-BR-README.md
└── CHANGELOG.md
```

<br>

## 🛡️ Recursos de Segurança

### **Autenticação e Autorização**
- **Tokens JWT:** Autenticação stateless segura
- **Refresh Tokens:** Renovação automática de tokens
- **Blacklist de Tokens:** Funcionalidade de logout seguro
- **Hash de Senhas:** bcryptjs com salt rounds

### **Validação de Dados**
- **Sanitização de Inputs:** Previne dados maliciosos
- **Política de Senha Forte:** 8+ caracteres, maiúscula, minúscula, números, símbolos
- **Proteção de Dados Pessoais:** Previne senhas contendo informações do usuário
- **Rate Limiting:** 5 tentativas de login a cada 15 minutos

### **Headers de Segurança HTTP**
- **Content Security Policy (CSP):** Proteção contra XSS
- **HTTP Strict Transport Security (HSTS):** Força HTTPS
- **X-Frame-Options:** Proteção contra clickjacking
- **X-Content-Type-Options:** Prevenção de MIME sniffing

<br>

## 🚀 Como Executar

### **Pré-requisitos**
- Node.js (v16 ou superior)
- npm ou yarn
- Git

### **Configuração do Backend**

```bash
# Clonar o repositório
git clone https://github.com/gabrielcamarate/Code2Post.git
cd Code2Post

# Instalar dependências do backend
cd backend
npm install

# Criar arquivo de ambiente
cp env.example .env
```

**Configure seu arquivo `.env`:**
```dotenv
PORT=3001
NODE_ENV=development

# JWT Secrets
JWT_SECRET=sua_chave_jwt_super_secreta_aqui
JWT_REFRESH_SECRET=sua_chave_refresh_super_secreta_aqui

# Gerenciamento de Sessões (Proteção CSRF)
SESSION_SECRET=sua_chave_sessao_super_secreta_aqui

# Frontend URL (para CORS)
FRONTEND_URL=http://localhost:5173

# GitHub OAuth
GITHUB_CLIENT_ID=seu_github_client_id
GITHUB_CLIENT_SECRET=seu_github_client_secret
GITHUB_CALLBACK_URL=http://localhost:3001/auth/github/callback

# Gemini API
GEMINI_API_KEY=sua_gemini_api_key
```

**Iniciar o backend:**
```bash
npm start
```

A API estará disponível em `http://localhost:3001`

### **Configuração do Frontend (Em Desenvolvimento)**

```bash
# Instalar dependências do frontend
cd ../frontend
npm install

# Iniciar servidor de desenvolvimento
npm run dev
```

O frontend estará disponível em `http://localhost:5173`

<br>

## 🧪 Testes da API

### **Endpoints de Autenticação**

**1. Login (com validação segura de senha):**
```bash
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Secure@2024"
  }'
```

**2. Verificar Token:**
```bash
curl -X GET http://localhost:3001/auth/verify \
  -H "Authorization: Bearer SEU_ACCESS_TOKEN"
```

**3. Renovar Token:**
```bash
curl -X POST http://localhost:3001/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "SEU_REFRESH_TOKEN"
  }'
```

**4. Logout (adiciona token à blacklist):**
```bash
curl -X POST http://localhost:3001/auth/logout \
  -H "Authorization: Bearer SEU_ACCESS_TOKEN"
```

### **Testes de Segurança**

**Testar proteção CSRF:**
```bash
# Obter token CSRF
curl -X GET http://localhost:3001/csrf-token -c cookies.txt

# Tentar fazer requisição sem token CSRF (deve falhar)
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrong"}'

# Fazer requisição com token CSRF (deve funcionar)
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: SEU_TOKEN_CSRF" \
  -b cookies.txt \
  -d '{"email":"<EMAIL>","password":"wrong"}'
```

**Testar rate limiting:**
```bash
# Fazer múltiplas requisições rapidamente
for i in {1..10}; do
  curl -X POST http://localhost:3001/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}'
done
```

**Testar validação de senha:**
```bash
# Senha fraca (deve falhar)
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123"}'
```

**Verificar headers de segurança:**
```bash
curl -I http://localhost:3001/
```

### **Health Check**
```bash
curl http://localhost:3001/health
```

<br>

## 🗺️ Roadmap de Desenvolvimento

### **Fase 1: Autenticação e Segurança ✅ (v1.2.3)**
- [x] Sistema de autenticação JWT com refresh tokens
- [x] Hash de senhas com bcryptjs
- [x] Validação e sanitização de inputs
- [x] Headers de segurança com helmet
- [x] Rate limiting para proteção contra força bruta
- [x] **Proteção CSRF com express-session**
- [x] **Gerenciamento de sessões e segurança**
- [x] Configuração HTTPS para produção
- [x] Testes e validação da API

### **Fase 2: Integração GitHub 🚧 (v1.2.0)**
- [x] Configuração OAuth do GitHub
- [x] Cliente GitHub API com Octokit
- [x] Listagem e gerenciamento de repositórios
- [x] Análise de histórico de commits
- [x] Monitoramento de pull requests
- [x] Integração de webhooks para atualizações em tempo real
- [x] Gerenciamento e validação de permissões
- [x] Renovação e gerenciamento de tokens
- [ ] Interface frontend para seleção de repositórios
- [ ] Dashboard de atividade de repositórios
- [ ] Visualização de análise de commits

### **Fase 3: Integração IA 🚧 (v1.2.0)**
- [x] Integração com API Gemini
- [x] Serviço de geração de posts
- [x] Geração de resumos técnicos
- [x] Geração de hashtags
- [x] Análise de commits e insights
- [x] Sistema de templates para posts
- [x] Cache e otimização de conteúdo
- [ ] Interface frontend para configuração de IA
- [ ] Preview e edição de posts
- [ ] UI de gerenciamento de templates
- [ ] Opções de personalização de conteúdo

### **Fase 4: Desenvolvimento Frontend 🚧 (v1.2.0)**
- [x] Configuração React + TypeScript
- [x] Configuração de build Vite
- [x] Integração Tailwind CSS
- [x] Componentes shadcn/ui
- [x] Contexto de autenticação e roteamento
- [x] Páginas de login e dashboard
- [x] Teste de conectividade com backend
- [x] **Componente de loading spinner customizado**
- [ ] Interface de seleção de repositórios
- [ ] Fluxo de geração de posts
- [ ] Edição e preview de posts
- [ ] Configurações e preferências do usuário
- [ ] Dashboard com estatísticas
- [ ] Histórico e gerenciamento de posts

### **Fase 5: Integração LinkedIn 📋 (Planejado)**
- [ ] Configuração OAuth do LinkedIn
- [ ] API de publicação de posts
- [ ] Agendamento de conteúdo
- [ ] Analytics e insights
- [ ] Publicação multi-plataforma

### **Fase 6: Produção e Deploy 📋 (Planejado)**
- [ ] Configuração de pipeline CI/CD
- [ ] Configuração de ambiente de produção
- [ ] Monitoramento e logging
- [ ] Otimização de performance
- [ ] Auditoria e hardening de segurança

<br>

## 📝 Licença

Este projeto é privado e para uso restrito. Distribuição, modificação ou uso comercial não é permitido sem autorização explícita do autor.

<br>

## 📬 Contato

Feito com ❤️ por Gabriel Camarate. Entre em contato!

[![LinkedIn](https://img.shields.io/badge/linkedin-%230077B5.svg?style=for-the-badge&logo=linkedin&logoColor=white)](https://www.linkedin.com/in/gabrielcamarate/)
[![Gmail](https://img.shields.io/badge/EMAIL-D14836?style=for-the-badge&logo=gmail&logoColor=white)](mailto:<EMAIL>)
[![GitHub](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white)](https://github.com/gabrielcamarate) 