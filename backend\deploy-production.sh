#!/bin/bash

# Script de Deploy para Produção - Code2Post
# Execute: chmod +x deploy-production.sh && ./deploy-production.sh

set -e  # Parar em caso de erro

echo "🚀 Code2Post - Deploy para Produção"
echo "=================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    log_error "Execute este script no diretório backend/"
    exit 1
fi

# Verificar se o arquivo .env existe
if [ ! -f ".env" ]; then
    log_error "Arquivo .env não encontrado. Copie de env.example e configure:"
    echo "cp env.example .env"
    echo "nano .env"
    exit 1
fi

# Verificar variáveis de ambiente críticas
log_info "Verificando configurações..."

if [ -z "$DOMAIN" ]; then
    log_error "DOMAIN não configurado no .env"
    exit 1
fi

if [ -z "$LETS_ENCRYPT_EMAIL" ]; then
    log_error "LETS_ENCRYPT_EMAIL não configurado no .env"
    exit 1
fi

log_success "Configurações básicas verificadas"

# Instalar dependências
log_info "Instalando dependências..."
npm ci --only=production

# Criar diretórios necessários
log_info "Criando diretórios..."
mkdir -p logs
mkdir -p greenlock.d

# Verificar permissões
log_info "Verificando permissões..."
if [ "$EUID" -eq 0 ]; then
    log_warning "Executando como root - certificados podem ter problemas de permissão"
fi

# Verificar se as portas estão disponíveis
log_info "Verificando portas..."
if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null ; then
    log_warning "Porta 80 já está em uso"
fi

if lsof -Pi :443 -sTCP:LISTEN -t >/dev/null ; then
    log_warning "Porta 443 já está em uso"
fi

# Backup de configurações existentes
if [ -d "greenlock.d" ] && [ "$(ls -A greenlock.d)" ]; then
    log_info "Fazendo backup das configurações existentes..."
    cp -r greenlock.d greenlock.d.backup.$(date +%Y%m%d_%H%M%S)
fi

# Configurar firewall (se disponível)
if command -v ufw &> /dev/null; then
    log_info "Configurando firewall..."
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw allow 3001/tcp
    log_success "Firewall configurado"
fi

# Verificar se o domínio aponta para este servidor
log_info "Verificando DNS do domínio $DOMAIN..."
DOMAIN_IP=$(dig +short $DOMAIN)
SERVER_IP=$(curl -s ifconfig.me)

if [ "$DOMAIN_IP" = "$SERVER_IP" ]; then
    log_success "DNS configurado corretamente"
else
    log_warning "DNS pode não estar configurado corretamente"
    echo "IP do domínio: $DOMAIN_IP"
    echo "IP do servidor: $SERVER_IP"
fi

# Testar configuração
log_info "Testando configuração..."
npm run code:check

# Iniciar servidor em modo teste
log_info "Iniciando servidor em modo teste..."
log_warning "Pressione Ctrl+C para parar o teste"

# Função para limpeza
cleanup() {
    log_info "Parando servidor..."
    pkill -f "node.*server-production.js" || true
    log_success "Deploy concluído!"
    exit 0
}

# Capturar Ctrl+C
trap cleanup SIGINT

# Iniciar servidor
NODE_ENV=production npm run start:prod &

# Aguardar um pouco para o servidor inicializar
sleep 5

# Testar endpoints
log_info "Testando endpoints..."

# Testar health check
if curl -f -s "https://$DOMAIN/health" > /dev/null; then
    log_success "Health check funcionando"
else
    log_warning "Health check não respondeu (pode ser normal durante inicialização)"
fi

# Testar redirecionamento HTTP → HTTPS
if curl -f -s -I "http://$DOMAIN" | grep -q "301\|302"; then
    log_success "Redirecionamento HTTP → HTTPS funcionando"
else
    log_warning "Redirecionamento HTTP → HTTPS não funcionou"
fi

log_success "Deploy concluído com sucesso!"
log_info "Para monitorar logs: tail -f logs/app.log"
log_info "Para parar o servidor: pkill -f 'node.*server-production.js'"

# Manter o script rodando para mostrar logs
log_info "Mostrando logs do servidor..."
tail -f logs/app.log 