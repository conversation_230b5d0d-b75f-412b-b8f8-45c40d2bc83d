import { getGitHubToken } from '../services/githubService.js';

/**
 * Middleware para validar se o usuário tem token GitHub válido
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next function
 */
function validateGitHubToken(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Usuário não autenticado',
        error_en: 'User not authenticated',
      });
    }

    // Verificar se o usuário tem token GitHub configurado
    const token = getGitHubToken(req.user.userId);

    if (!token) {
      return res.status(400).json({
        error:
          'Token GitHub não configurado. Configure seu token GitHub primeiro.',
        error_en:
          'GitHub token not configured. Please configure your GitHub token first.',
      });
    }

    next();
  } catch (error) {
    console.error('Erro na validação do token GitHub:', error);
    res.status(500).json({
      error: 'Erro interno na validação do token GitHub',
      error_en: 'Internal error validating GitHub token',
    });
  }
}

export { validateGitHubToken };
