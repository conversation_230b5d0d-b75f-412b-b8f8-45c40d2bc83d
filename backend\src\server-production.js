import greenlock from 'greenlock-express';
import { getConfig, isProduction } from './config/production.js';
import app from './app.js';

/**
 * Servidor de produção com HTTPS automático
 */
export const startProductionServer = () => {
  const config = getConfig();

  console.log('🚀 Iniciando servidor de produção...');
  console.log(`📊 Ambiente: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Domínio: ${config.domain.main}`);

  if (config.ssl.letsEncrypt.enabled) {
    console.log("🔒 Let's Encrypt habilitado");
    startLetsEncryptServer(config);
  } else if (config.ssl.manual.enabled) {
    console.log('🔒 Certificados manuais habilitados');
    startManualSSLServer(config);
  } else {
    console.log('⚠️ HTTPS desabilitado - usando HTTP');
    startHTTPOnlyServer(config);
  }
};

/**
 * Servidor com Let's Encrypt (produção)
 */
const startLetsEncryptServer = config => {
  greenlock.init({
    packageRoot: process.cwd(),
    configDir: './greenlock.d',

    // Configurações do Let's Encrypt
    maintainerEmail: config.ssl.letsEncrypt.email,
    cluster: false, // true para múltiplas instâncias

    // Domínios para certificados
    notify: (event, details) => {
      if (event === 'error') {
        console.error("❌ Erro no Let's Encrypt:", details);
      }
    },
  });

  // Adicionar domínios
  const domains = [
    config.domain.main,
    config.domain.www,
    ...config.domain.altDomains,
  ].filter(Boolean);

  domains.forEach(domain => {
    greenlock.add({
      subject: domain,
      altnames: [domain],
    });
  });

  // Servir a aplicação
  greenlock.serve(app);

  console.log(`✅ Servidor HTTPS iniciado em https://${config.domain.main}`);
  console.log(`📋 Domínios configurados: ${domains.join(', ')}`);
};

/**
 * Servidor com certificados manuais
 */
const startManualSSLServer = config => {
  import('https').then(https => {
    import('fs').then(fs => {
      try {
        const httpsOptions = {
          cert: fs.readFileSync(config.ssl.manual.certPath),
          key: fs.readFileSync(config.ssl.manual.keyPath),
          ca: fs.existsSync(config.ssl.manual.caPath)
            ? fs.readFileSync(config.ssl.manual.caPath)
            : undefined,
        };

        const httpsServer = https.createServer(httpsOptions, app);

        httpsServer.listen(config.server.httpsPort, () => {
          console.log(
            `✅ Servidor HTTPS iniciado na porta ${config.server.httpsPort}`
          );
          console.log(
            `🔒 Certificados carregados de: ${config.ssl.manual.certPath}`
          );
        });

        // Redirecionar HTTP para HTTPS
        if (isProduction()) {
          import('http').then(http => {
            const httpApp = (req, res) => {
              res.writeHead(301, {
                Location: `https://${req.headers.host}${req.url}`,
              });
              res.end();
            };

            http.createServer(httpApp).listen(config.server.httpPort, () => {
              console.log(
                `🔄 Redirecionamento HTTP → HTTPS na porta ${config.server.httpPort}`
              );
            });
          });
        }
      } catch (error) {
        console.error('❌ Erro ao carregar certificados SSL:', error.message);
        console.log('🔄 Fallback para servidor HTTP...');
        startHTTPOnlyServer(config);
      }
    });
  });
};

/**
 * Servidor HTTP apenas (desenvolvimento)
 */
const startHTTPOnlyServer = config => {
  app.listen(config.server.port, () => {
    console.log(`✅ Servidor HTTP iniciado na porta ${config.server.port}`);
    console.log(
      `📊 Health check: http://localhost:${config.server.port}/health`
    );
  });
};

/**
 * Middleware de redirecionamento HTTP → HTTPS
 */
export const redirectToHTTPS = (req, res, next) => {
  if (isProduction() && !req.secure) {
    return res.redirect(`https://${req.headers.host}${req.url}`);
  }
  next();
};

/**
 * Middleware de headers de segurança para produção
 */
export const productionSecurityHeaders = (req, res, next) => {
  if (isProduction()) {
    const config = getConfig();

    // HSTS
    res.setHeader(
      'Strict-Transport-Security',
      `max-age=${config.security.headers.hsts.maxAge}; includeSubDomains; preload`
    );

    // CSP
    const csp = config.security.headers.csp;
    const cspString = Object.entries(csp)
      .map(([key, values]) => `${key} ${values.join(' ')}`)
      .join('; ');

    res.setHeader('Content-Security-Policy', cspString);

    // Outros headers de segurança
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  next();
};

// Iniciar servidor se executado diretamente
if (import.meta.url.endsWith(process.argv[1])) {
  startProductionServer();
}
