<div align="center">
  <img src="/frontend/public/png/logo-4x.png" alt="Code2Post" width="250" height="250">



  **Automate and share your development progress on LinkedIn, directly from your GitHub, powered by AI.**

  [🇺🇸 English Version](README.md) | [🇧🇷 Versão em Português](PT-BR-README.md)
</div>
<div align="center">

![GitHub language count](https://img.shields.io/github/languages/count/gabrielcamarate/Code2Post?style=for-the-badge)
![Version](https://img.shields.io/badge/version-v2.0.0-blue?style=for-the-badge)
![Status](https://img.shields.io/badge/status-frontend_complete-green?style=for-the-badge)

</div>


<br>

<p align="center">
  <!-- Add your project preview image below -->
  <img src="images/preview.png" alt="Code2Post Preview" width="800"/>
</p>

> ### 🔗 **[Live Demo - www.code2post.com](https://www.code2post.com)**

<br>

## 📋 Table of Contents

* [About](#-about)
* [Features](#-features)
* [Technologies Used](#-technologies-used)
* [Architecture](#-architecture)
* [Security Features](#-security-features)
* [How to Run](#-how-to-run)
* [API Testing](#-api-testing)
* [Development Roadmap](#-development-roadmap)
* [License](#-license)
* [Contact](#-contact)

<br>

## 📖 About

**Code2Post** is a professional SaaS platform that connects your GitHub to LinkedIn, automatically generating post suggestions about your project progress, using artificial intelligence to create contextual and engaging content.

**Current Status:** ✅ **FRONTEND COMPLETE!** Professional dashboard with 7 main pages, complete CRUD functionality, responsive design, and modern UI. Backend fully integrated with GitHub API, Gemini AI, and secure authentication system.

## ✨ Features

### 🎯 **Complete Dashboard Suite**
- 📊 **Dashboard** - Overview with metrics and recent activities
- ⏰ **Timeline** - GitHub activity timeline with visual progress
- 📁 **Repositories** - GitHub repository management and selection
- 📝 **Posts** - LinkedIn post management with authentic preview
- 📈 **Analytics** - Advanced metrics and performance tracking
- 🗓️ **Scheduling** - Complete post scheduling system with CRUD
- ⚙️ **Settings** - User preferences and subscription management

### 🔧 **Advanced Functionality**
- 🔐 **GitHub OAuth Authentication** - Secure account connection
- 🤖 **AI-Powered Content** - Gemini AI for intelligent post generation
- 📱 **Responsive Design** - Mobile-first approach, works on all devices
- 🎨 **Modern UI** - Glassmorphism design with dark theme
- 🔍 **Advanced Filtering** - Search and filter across all data
- 📊 **Real-time Metrics** - Live engagement and performance data
- 💳 **Subscription System** - Professional pricing tiers (Free, Pro, Enterprise)

### 🛡️ **Security & Performance**
- 🔒 **JWT Authentication** with refresh tokens
- 🛡️ **CSRF Protection** and rate limiting
- 📡 **API Integration** - GitHub API and Gemini AI
- ⚡ **Optimized Performance** - Fast loading and smooth interactions
- 🔄 **State Management** - Efficient data handling and caching

---

## 🛠️ Tecnologias

### Backend
- **Node.js** + **Express.js**
- **JWT Authentication** com refresh tokens
- **GitHub OAuth** integration
- **Google Gemini AI** para geração de conteúdo
- **Rate Limiting** e **CSRF Protection**
- **HTTPS** com SSL automático

### Frontend
- **React 18** + **TypeScript**
- **Tailwind CSS** para estilização
- **shadcn/ui** para componentes
- **React Query** para cache
- **React Router** para navegação

---

## 🚀 Quick Start

### Pré-requisitos
- Node.js 18+
- npm ou yarn
- Conta GitHub
- Chave API Gemini (Google AI)

### Instalação

1. **Clone o repositório**
```bash
git clone https://github.com/seu-usuario/code2post.git
cd code2post
```

2. **Configure o Backend**
```bash
cd backend
npm install
cp env.example .env
# Configure suas variáveis de ambiente
npm run dev
```

3. **Configure o Frontend**
```bash
cd frontend
npm install
npm run dev
```

4. **Configure as APIs**
- GitHub OAuth App
- Google Gemini API
- Variáveis de ambiente

---

## 📁 Estrutura do Projeto

```
Code2Post/
├── backend/                # API Node.js/Express
│   ├── src/
│   │   ├── routes/         # Rotas da API
│   │   ├── services/       # Serviços externos
│   │   ├── middleware/     # Middlewares
│   │   └── app.js         # Aplicação principal
│   └── package.json
├── frontend/               # Aplicação React
│   ├── src/
│   │   ├── components/     # Componentes UI
│   │   ├── pages/         # Páginas
│   │   ├── services/      # Serviços API
│   │   └── contexts/      # Contextos React
│   └── package.json
└── README.md
```

---

## 🔧 Configuração

### Variáveis de Ambiente (Backend)

```env
# Server
PORT=3000
NODE_ENV=development

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key

# JWT
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret

# Security
CSRF_SECRET=your_csrf_secret
```

### Variáveis de Ambiente (Frontend)

```env
VITE_API_URL=http://localhost:3000
VITE_GITHUB_CLIENT_ID=your_github_client_id
```

---

## 📖 Documentação

- [Guia de Instalação](docs/INSTALLATION.md)
- [Configuração de APIs](docs/APIS.md)
- [Deploy em Produção](docs/DEPLOYMENT.md)
- [Contribuição](CONTRIBUTING.md)

---



---

## 🔢 Versioning

This project follows [Semantic Versioning](https://semver.org/) (MAJOR.MINOR.PATCH):

### Version Control Rules
- **ALWAYS check current tag** before creating new one: `git tag | tail -1`
- **Follow sequence** correctly: v1.3.1 → v1.3.2 → v1.3.3
- **Use semantic versioning**: MAJOR.MINOR.PATCH
- **Update changelog** for every version change
- **Push tags** to remote repository

### Version Types
- **MAJOR** (1.0.0): Breaking changes, major features
- **MINOR** (1.1.0): New features, backward compatible
- **PATCH** (1.1.1): Bug fixes, minor improvements

---

## 📄 License

This project is private and for restricted use. Distribution, modification, or commercial use is not permitted without explicit authorization from the author.

---

## 🙏 Agradecimentos

- [GitHub API](https://docs.github.com/en/rest) - Para integração com repositórios
- [Google Gemini AI](https://ai.google.dev/) - Para geração de conteúdo inteligente
- [shadcn/ui](https://ui.shadcn.com/) - Para componentes UI incríveis
- [Tailwind CSS](https://tailwindcss.com/) - Para estilização moderna

---

## 📬 Contact

Made with ❤️ by Gabriel Camarate. Get in touch!

[![LinkedIn](https://img.shields.io/badge/linkedin-%230077B5.svg?style=for-the-badge&logo=linkedin&logoColor=white)](https://www.linkedin.com/in/gabrielcamarate/)
[![Gmail](https://img.shields.io/badge/EMAIL-D14836?style=for-the-badge&logo=gmail&logoColor=white)](mailto:<EMAIL>)
[![GitHub](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white)](https://github.com/gabrielcamarate) 
