import { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Github, CheckCircle, XCircle, Loader2, Sparkles } from 'lucide-react';

export default function GitHubCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { loginWithGitHub } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const hasProcessed = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      // Evitar múltiplas execuções (React StrictMode protection)
      if (hasProcessed.current || isProcessing) {
        console.log('🔄 Processamento já iniciado, ignorando...');
        return;
      }

      hasProcessed.current = true;
      setIsProcessing(true);

      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      // Verificar se houve erro na autorização
      if (error) {
        setError(`Erro na autorização: ${error}`);
        setLoading(false);
        return;
      }

      // Verificar se temos o código
      if (!code) {
        setError('Código de autorização não recebido');
        setLoading(false);
        return;
      }

      // Verificar se já processamos este código
      const processedCode = localStorage.getItem('processed_github_code');
      if (processedCode === code) {
        console.log('🔄 Código já processado, verificando se usuário está logado...');

        // Verificar se o usuário já está logado
        const githubUser = localStorage.getItem('githubUser');
        if (githubUser) {
          console.log('✅ Usuário já logado, redirecionando para dashboard...');
          navigate('/dashboard');
          return;
        }

        // Se não está logado, mostrar erro
        console.log('⚠️ Código já processado mas falhou');
        setError('Código de autorização já foi utilizado. Tente fazer login novamente.');
        setLoading(false);
        setIsProcessing(false);
        return;
      }

      try {
        console.log('🔄 Processando callback do GitHub...');
        console.log('📝 Code:', code ? 'Recebido' : 'Não recebido');
        console.log('🔒 State:', state);

        // Marcar código como processado ANTES da requisição
        localStorage.setItem('processed_github_code', code);

        // Fazer requisição para o backend processar o callback
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
        const response = await fetch(`${apiUrl}/auth/github/callback?code=${code}&state=${state}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Erro no servidor');
        }

        if (!data.success) {
          throw new Error(data.error || 'Erro na autenticação');
        }

        console.log('✅ Autenticação GitHub realizada com sucesso');
        console.log('👤 Usuário:', data.user.login);

        // Salvar dados do usuário e tokens
        localStorage.setItem('accessToken', data.accessToken);  // Token JWT
        localStorage.setItem('githubToken', data.githubToken);  // Token GitHub
        localStorage.setItem('githubUser', JSON.stringify(data.user));

        // Fazer login no contexto
        loginWithGitHub(data.user);

        // Mostrar tela de sucesso
        setUserInfo(data.user);
        setSuccess(true);
        setLoading(false);

        // Aguardar 2 segundos antes de redirecionar para mostrar a tela de sucesso
        // Usar requestAnimationFrame para evitar violações de performance
        const redirectTimer = setTimeout(() => {
          requestAnimationFrame(() => {
            navigate('/dashboard');
          });
        }, 2000);

      } catch (error) {
        console.error('❌ Erro no callback GitHub:', error);
        setError(error instanceof Error ? error.message : 'Erro desconhecido');
        toast.error('Erro na autenticação GitHub');
        
        // Limpar código processado em caso de erro
        localStorage.removeItem('processed_github_code');
      } finally {
        setLoading(false);
        setIsProcessing(false);
      }
    };

    processCallback();
  }, [searchParams, navigate, loginWithGitHub]); // Removido isProcessing da dependência

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        {/* Floating Icons */}
        <div className="absolute inset-0 pointer-events-none z-10">
          <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
            <Github size={24} />
          </div>
          <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
            <Sparkles size={24} />
          </div>
          <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
            <CheckCircle size={24} />
          </div>
        </div>

        <div className="flex items-center justify-center min-h-screen p-4 relative z-20">
          <Card className="w-full max-w-lg bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Github className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Conectando com GitHub
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                Processando sua autenticação...
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="relative">
                <div className="w-12 h-12 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto" />
                <div className="absolute inset-0 w-12 h-12 border-4 border-purple-500/20 border-b-purple-500 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }} />
              </div>

              <div className="space-y-2">
                <p className="text-slate-300 font-medium">Validando credenciais...</p>
                <div className="flex justify-center space-x-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce delay-100" />
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce delay-200" />
                </div>
              </div>

              <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
                <p className="text-slate-400 text-sm">
                  🔐 Estabelecendo conexão segura com GitHub
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        {/* Background Elements - Igual às outras páginas */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        {/* Floating Icons - Igual às outras páginas */}
        <div className="absolute inset-0 pointer-events-none z-10">
          <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
            <Github size={24} />
          </div>
          <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
            <Sparkles size={24} />
          </div>
          <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
            <XCircle size={24} />
          </div>
          <div className="absolute top-64 right-32 text-red-400/20 animate-bounce delay-1000">
            <XCircle size={20} />
          </div>
          <div className="absolute bottom-48 right-64 text-orange-400/20 animate-bounce delay-500">
            <Github size={22} />
          </div>
        </div>

        <div className="flex items-center justify-center min-h-screen p-4 relative z-20">
          <Card className="w-full max-w-lg bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4 relative">
                <XCircle className="w-8 h-8 text-white" />
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-400 to-orange-500 animate-ping opacity-30" />
              </div>
              <CardTitle className="text-2xl bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
                Erro na Autenticação
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                Não foi possível conectar com GitHub
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <p className="text-red-300 text-center font-medium">{error}</p>
              </div>

              <div className="space-y-3">
                <p className="text-slate-400 text-sm text-center">
                  Possíveis soluções:
                </p>
                <ul className="text-slate-400 text-sm space-y-2">
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-400 rounded-full" />
                    Verifique sua conexão com a internet
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-400 rounded-full" />
                    Tente fazer login novamente
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                    Verifique se o GitHub está funcionando
                  </li>
                </ul>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={() => navigate('/login')}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Voltar ao Login
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-800 hover:text-white transition-all duration-300 hover:scale-105"
                >
                  Tentar Novamente
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (success && userInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        {/* Background Elements - Igual às outras páginas */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        {/* Floating Icons - Igual às outras páginas */}
        <div className="absolute inset-0 pointer-events-none z-10">
          <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
            <Github size={24} />
          </div>
          <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
            <Sparkles size={24} />
          </div>
          <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
            <CheckCircle size={24} />
          </div>
          <div className="absolute top-64 right-32 text-green-400/20 animate-bounce delay-1000">
            <CheckCircle size={20} />
          </div>
          <div className="absolute bottom-48 right-64 text-blue-400/20 animate-bounce delay-500">
            <Github size={22} />
          </div>
        </div>

        <div className="flex items-center justify-center min-h-screen p-4 relative z-20">
          <Card className="w-full max-w-lg bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4 relative">
                <CheckCircle className="w-8 h-8 text-white" />
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 animate-ping opacity-30" />
              </div>
              <CardTitle className="text-2xl bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                Conectado com Sucesso!
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                Bem-vindo ao Code2Post
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
                <img
                  src={userInfo.avatar_url}
                  alt={userInfo.login}
                  className="w-12 h-12 rounded-full border-2 border-green-400/50"
                />
                <div>
                  <p className="font-semibold text-white">{userInfo.name || userInfo.login}</p>
                  <p className="text-sm text-slate-400">@{userInfo.login}</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3 text-green-400">
                  <CheckCircle size={16} />
                  <span className="text-sm">Conta GitHub conectada</span>
                </div>
                <div className="flex items-center gap-3 text-green-400">
                  <CheckCircle size={16} />
                  <span className="text-sm">Repositórios sincronizados</span>
                </div>
                <div className="flex items-center gap-3 text-green-400">
                  <CheckCircle size={16} />
                  <span className="text-sm">Redirecionando para o dashboard...</span>
                </div>
              </div>

              <div className="flex items-center justify-center gap-2 text-slate-400 text-sm">
                <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                <span>Preparando sua experiência personalizada</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Fallback - não deveria chegar aqui
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-xl text-slate-300">Processando...</CardTitle>
        </CardHeader>
      </Card>
    </div>
  );
}