import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import {
  GitBranch,
  Sparkles,
  Zap,
  Users,
  TrendingUp,
  Code,
  Rocket,
  Heart,
  Target,
  Globe,
  Database,
  Cpu,
  Wifi,
  Home,
  FolderGit2,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Bell,
  Menu,
  Calendar,
  Clock,
  X
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Mouse tracking para efeitos interativos
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      requestAnimationFrame(() => {
        setMousePosition({
          x: e.clientX,
          y: e.clientY + window.scrollY
        });
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll tracking para ícones flutuantes
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    logout();
    toast.success('Logout realizado com sucesso!');
    navigate('/login');
  };

  const sidebarItems = [
    { icon: Home, label: 'Dashboard', path: '/dashboard' },
    { icon: FolderGit2, label: 'Repositórios', path: '/repositories' },
    { icon: Clock, label: 'Timeline', path: '/timeline' },
    { icon: FileText, label: 'Posts', path: '/posts' },
    { icon: Calendar, label: 'Agendamento', path: '/scheduling' },
    { icon: BarChart3, label: 'Analytics', path: '/analytics' },
    { icon: Settings, label: 'Configurações', path: '/settings' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements - Igual às outras páginas */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>

        {/* Elemento que segue o mouse */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-blue-500/8 to-purple-500/8 rounded-full blur-3xl pointer-events-none"
          style={{
            transform: `translate(${mousePosition.x - 192}px, ${mousePosition.y - 192}px)`,
            transition: 'none',
          }}
        ></div>

        {/* Segundo elemento com delay menor */}
        <div
          className="absolute w-64 h-64 bg-gradient-to-r from-cyan-500/6 to-pink-500/6 rounded-full blur-2xl pointer-events-none"
          style={{
            transform: `translate(${mousePosition.x - 128}px, ${mousePosition.y - 128}px)`,
            transition: 'transform 0.1s ease-out',
          }}
        ></div>
      </div>

      {/* Floating Icons - Igual às outras páginas */}
      <div className="absolute inset-0 pointer-events-none z-10">
        <div
          className="absolute text-blue-400/20 animate-bounce transition-transform duration-1000 ease-out"
          style={{
            top: `${32 + scrollY * 0.1}px`,
            left: '32px',
            transform: `translateY(${Math.sin(scrollY * 0.01) * 10}px)`
          }}
        >
          <GitBranch size={24} />
        </div>
        <div
          className="absolute text-purple-400/20 animate-bounce delay-300 transition-transform duration-1000 ease-out"
          style={{
            top: `${48 + scrollY * 0.15}px`,
            right: '48px',
            transform: `translateY(${Math.cos(scrollY * 0.01) * 15}px)`
          }}
        >
          <Sparkles size={24} />
        </div>
        <div
          className="absolute text-cyan-400/20 animate-bounce delay-700 transition-transform duration-1000 ease-out"
          style={{
            bottom: `${32 + scrollY * 0.12}px`,
            left: '48px',
            transform: `translateY(${Math.sin(scrollY * 0.015) * 12}px)`
          }}
        >
          <Zap size={24} />
        </div>
        <div
          className="absolute text-green-400/20 animate-bounce delay-1000 transition-transform duration-1000 ease-out"
          style={{
            top: `${256 + scrollY * 0.08}px`,
            right: '128px',
            transform: `translateY(${Math.sin(scrollY * 0.012) * 8}px)`
          }}
        >
          <TrendingUp size={24} />
        </div>
        <div
          className="absolute text-pink-400/20 animate-bounce delay-500 transition-transform duration-1000 ease-out"
          style={{
            bottom: `${192 + scrollY * 0.09}px`,
            right: '256px',
            transform: `translateY(${Math.cos(scrollY * 0.008) * 10}px)`
          }}
        >
          <Users size={24} />
        </div>

        {/* Ícones adicionais espalhados */}
        <div
          className="absolute text-orange-400/20 animate-bounce delay-200 transition-transform duration-1000 ease-out"
          style={{
            top: `${450 + scrollY * 0.11}px`,
            right: '15%',
            transform: `translateY(${Math.sin(scrollY * 0.013) * 14}px)`
          }}
        >
          <Rocket size={20} />
        </div>
        <div
          className="absolute text-red-400/20 animate-bounce delay-800 transition-transform duration-1000 ease-out"
          style={{
            top: `${800 + scrollY * 0.07}px`,
            left: '8%',
            transform: `translateY(${Math.cos(scrollY * 0.011) * 9}px)`
          }}
        >
          <Heart size={18} />
        </div>
        <div
          className="absolute text-yellow-400/20 animate-bounce delay-1200 transition-transform duration-1000 ease-out"
          style={{
            top: `${1200 + scrollY * 0.13}px`,
            right: '25%',
            transform: `translateY(${Math.sin(scrollY * 0.009) * 11}px)`
          }}
        >
          <Target size={22} />
        </div>
        <div
          className="absolute text-indigo-400/20 animate-bounce delay-600 transition-transform duration-1000 ease-out"
          style={{
            top: `${1600 + scrollY * 0.14}px`,
            left: '12%',
            transform: `translateY(${Math.cos(scrollY * 0.014) * 13}px)`
          }}
        >
          <Code size={20} />
        </div>
        <div
          className="absolute text-emerald-400/20 animate-bounce delay-400 transition-transform duration-1000 ease-out"
          style={{
            top: `${2000 + scrollY * 0.10}px`,
            right: '5%',
            transform: `translateY(${Math.sin(scrollY * 0.016) * 7}px)`
          }}
        >
          <Globe size={19} />
        </div>
        <div
          className="absolute text-violet-400/20 animate-bounce delay-900 transition-transform duration-1000 ease-out"
          style={{
            top: `${2400 + scrollY * 0.06}px`,
            left: '18%',
            transform: `translateY(${Math.cos(scrollY * 0.010) * 12}px)`
          }}
        >
          <Database size={21} />
        </div>
        <div
          className="absolute text-teal-400/20 animate-bounce delay-1100 transition-transform duration-1000 ease-out"
          style={{
            top: `${2800 + scrollY * 0.08}px`,
            right: '30%',
            transform: `translateY(${Math.sin(scrollY * 0.012) * 8}px)`
          }}
        >
          <Cpu size={18} />
        </div>
        <div
          className="absolute text-sky-400/20 animate-bounce delay-1300 transition-transform duration-1000 ease-out"
          style={{
            top: `${3200 + scrollY * 0.09}px`,
            left: '22%',
            transform: `translateY(${Math.cos(scrollY * 0.015) * 10}px)`
          }}
        >
          <Wifi size={20} />
        </div>
      </div>

      {/* Header */}
      <header className="relative z-50 bg-slate-800/50 backdrop-blur-xl border-b border-slate-700/50">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo e Menu Mobile */}
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden text-white hover:bg-slate-700"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
              </Button>
              
              <div className="flex items-center space-x-2 group">
                <div className="w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <img src="/svg/icon-32.svg" alt="Code2Post" className="w-8 h-8" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Code2Post
                </span>
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white hover:bg-slate-700">
                <Bell size={18} />
              </Button>
              
              <div className="flex items-center space-x-3">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user?.avatar} />
                  <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm">
                    {user?.name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:block">
                  <p className="text-sm font-medium text-white">{user?.name}</p>
                  <p className="text-xs text-slate-400">{user?.email}</p>
                </div>
              </div>
              
              <Button 
                onClick={handleLogout} 
                variant="ghost" 
                size="sm"
                className="text-slate-300 hover:text-white hover:bg-slate-700"
              >
                <LogOut size={18} />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex relative z-20">
        {/* Sidebar */}
        <aside className={`
          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-slate-800/50 backdrop-blur-xl border-r border-slate-700/50
          transform transition-transform duration-300 ease-in-out lg:translate-x-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full pt-6">
            <nav className="flex-1 px-4 space-y-2">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                return (
                  <button
                    key={item.path}
                    onClick={() => navigate(item.path)}
                    className={`
                      w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
                      ${isActive
                        ? 'bg-gradient-to-r from-blue-600/20 to-purple-600/20 text-blue-300 border border-blue-500/30'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      }
                    `}
                  >
                    <Icon size={18} className="mr-3" />
                    {item.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </aside>

        {/* Overlay para mobile */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
}
