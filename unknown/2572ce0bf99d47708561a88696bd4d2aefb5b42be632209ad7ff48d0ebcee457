# Configurações do Servidor
PORT=3001
NODE_ENV=development

# JWT Secrets
JWT_SECRET=your_super_secret_jwt_key_here
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here

# Sessões (CSRF Protection)
SESSION_SECRET=your_session_secret_key_here

# Frontend URL (para CORS)
FRONTEND_URL=http://localhost:5173

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=http://localhost:3001/auth/github/callback

# Gemini API
GEMINI_API_KEY=your_gemini_api_key

# ========================================
# PRODUCTION CONFIGURATION
# ========================================

# Environment
NODE_ENV=production

# Domain Configuration
DOMAIN=code2post.com
WWW_DOMAIN=www.code2post.com
ALT_DOMAINS=api.code2post.com

# SSL/HTTPS Configuration
LETS_ENCRYPT_ENABLED=true
LETS_ENCRYPT_EMAIL=<EMAIL>
LETS_ENCRYPT_STAGING=false

# Manual SSL (fallback)
MANUAL_SSL_ENABLED=false
SSL_CERT_PATH=/path/to/certificate.pem
SSL_KEY_PATH=/path/to/private-key.pem
SSL_CA_PATH=/path/to/ca-bundle.pem

# Server Ports
HTTP_PORT=80
HTTPS_PORT=443

# Frontend URL (HTTPS)
FRONTEND_URL=https://code2post.com

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Database (future)
DATABASE_URL=postgresql://user:password@localhost:5432/code2post
DATABASE_SSL=true

# LinkedIn API (opcional)
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# HTTPS (Produção)
HTTPS_PORT=3443
SSL_KEY_PATH=ssl/private-key.pem
SSL_CERT_PATH=ssl/certificate.pem
SSL_CA_PATH=ssl/ca-bundle.pem 