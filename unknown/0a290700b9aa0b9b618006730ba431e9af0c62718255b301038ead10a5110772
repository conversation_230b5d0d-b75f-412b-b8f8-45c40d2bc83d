import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { GitBranch, FileText, GitCommit, Star, Eye, MessageCircle } from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'commit' | 'post' | 'star' | 'view';
  title: string;
  description: string;
  time: string;
  repository?: string;
  author?: {
    name: string;
    avatar: string;
  };
  stats?: {
    likes?: number;
    comments?: number;
    views?: number;
  };
}

export default function ActivityFeed() {
  // Dados fake para demonstração
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'commit',
      title: 'feat: add new dashboard components',
      description: 'Implementação de novos componentes para o dashboard com design system moderno',
      time: '2 horas atrás',
      repository: 'Code2Post',
      author: {
        name: '<PERSON>',
        avatar: '/api/placeholder/32/32'
      }
    },
    {
      id: '2',
      type: 'post',
      title: 'Post publicado no LinkedIn',
      description: 'Compartilhando progresso do desenvolvimento do dashboard',
      time: '4 horas atrás',
      stats: {
        likes: 23,
        comments: 5,
        views: 156
      }
    },
    {
      id: '3',
      type: 'commit',
      title: 'fix: resolve authentication issues',
      description: 'Correção de problemas na autenticação GitHub OAuth',
      time: '1 dia atrás',
      repository: 'Code2Post',
      author: {
        name: 'Gabriel Camarate',
        avatar: '/api/placeholder/32/32'
      }
    },
    {
      id: '4',
      type: 'star',
      title: 'Repositório recebeu nova estrela',
      description: 'Code2Post agora tem 15 estrelas no GitHub',
      time: '2 dias atrás',
      repository: 'Code2Post'
    }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case 'commit':
        return <GitCommit className="w-4 h-4" />;
      case 'post':
        return <FileText className="w-4 h-4" />;
      case 'star':
        return <Star className="w-4 h-4" />;
      default:
        return <GitBranch className="w-4 h-4" />;
    }
  };

  const getBadgeVariant = (type: string) => {
    switch (type) {
      case 'commit':
        return 'secondary';
      case 'post':
        return 'default';
      case 'star':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'commit':
        return 'text-blue-400 border-blue-500/30 bg-blue-500/10';
      case 'post':
        return 'text-green-400 border-green-500/30 bg-green-500/10';
      case 'star':
        return 'text-yellow-400 border-yellow-500/30 bg-yellow-500/10';
      default:
        return 'text-slate-400 border-slate-500/30 bg-slate-500/10';
    }
  };

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <GitBranch className="w-5 h-5 mr-2 text-blue-400" />
          Atividade Recente
        </CardTitle>
        <CardDescription className="text-slate-300">
          Últimos commits, posts e interações
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-start space-x-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600/30 hover:bg-slate-700/50 transition-all duration-200"
            >
              {/* Avatar ou Ícone */}
              <div className="flex-shrink-0">
                {activity.author ? (
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={activity.author.avatar} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs">
                      {activity.author.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center">
                    {getIcon(activity.type)}
                  </div>
                )}
              </div>

              {/* Conteúdo */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <Badge 
                    variant={getBadgeVariant(activity.type)}
                    className={getBadgeColor(activity.type)}
                  >
                    {getIcon(activity.type)}
                    <span className="ml-1 capitalize">{activity.type}</span>
                  </Badge>
                  {activity.repository && (
                    <span className="text-xs text-slate-400">
                      {activity.repository}
                    </span>
                  )}
                </div>
                
                <h4 className="text-sm font-medium text-white mb-1">
                  {activity.title}
                </h4>
                
                <p className="text-xs text-slate-400 mb-2">
                  {activity.description}
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">
                    {activity.time}
                  </span>

                  {/* Stats para posts */}
                  {activity.stats && (
                    <div className="flex items-center space-x-3 text-xs text-slate-400">
                      {activity.stats.likes && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3" />
                          <span>{activity.stats.likes}</span>
                        </div>
                      )}
                      {activity.stats.comments && (
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{activity.stats.comments}</span>
                        </div>
                      )}
                      {activity.stats.views && (
                        <div className="flex items-center space-x-1">
                          <Eye className="w-3 h-3" />
                          <span>{activity.stats.views}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Ação */}
              <div className="flex-shrink-0">
                {activity.type === 'commit' ? (
                  <Button size="sm" variant="outline" className="text-xs">
                    Gerar Post
                  </Button>
                ) : activity.type === 'post' ? (
                  <Button size="sm" variant="outline" className="text-xs">
                    Ver Post
                  </Button>
                ) : (
                  <Button size="sm" variant="ghost" className="text-xs">
                    Ver
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Ver mais */}
        <div className="mt-4 text-center">
          <Button variant="ghost" className="text-blue-400 hover:text-blue-300">
            Ver todas as atividades
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
