#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const environment = process.argv[2];

if (!environment || !['development', 'production'].includes(environment)) {
  console.log('❌ Uso: node scripts/switch-env.js [development|production]');
  process.exit(1);
}

console.log(`🔄 Mudando para ambiente: ${environment}`);

// Frontend
const frontendEnvSource = path.join(__dirname, `../frontend/.env.${environment}`);
const frontendEnvTarget = path.join(__dirname, '../frontend/.env');

// Backend
const backendEnvSource = path.join(__dirname, `../backend/.env.${environment}`);
const backendEnvTarget = path.join(__dirname, '../backend/.env');

try {
  // Copiar arquivos de ambiente
  fs.copyFileSync(frontendEnvSource, frontendEnvTarget);
  fs.copyFileSync(backendEnvSource, backendEnvTarget);
  
  console.log('✅ Frontend .env atualizado');
  console.log('✅ Backend .env atualizado');
  console.log(`🎯 Ambiente configurado para: ${environment.toUpperCase()}`);
  
  if (environment === 'development') {
    console.log('🚀 Para iniciar desenvolvimento:');
    console.log('   Frontend: cd frontend && npm run dev');
    console.log('   Backend:  cd backend && npm run dev');
  } else {
    console.log('🚀 Para build de produção:');
    console.log('   Frontend: cd frontend && npm run build');
    console.log('   Backend:  cd backend && npm start');
  }
  
} catch (error) {
  console.error('❌ Erro ao trocar ambiente:', error.message);
  process.exit(1);
}
