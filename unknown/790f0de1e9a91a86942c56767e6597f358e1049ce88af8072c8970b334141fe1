import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Eye, 
  ThumbsUp, 
  MessageCircle, 
  Share,
  Calendar,
  Clock,
  Target,
  Zap,
  Award,
  RefreshCw,
  Download,
  Filter,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface AnalyticsData {
  overview: {
    totalPosts: number;
    totalReach: number;
    totalEngagement: number;
    avgEngagementRate: number;
    followers: number;
    profileViews: number;
  };
  trends: {
    postsGrowth: number;
    reachGrowth: number;
    engagementGrowth: number;
    followersGrowth: number;
  };
  topPosts: Array<{
    id: string;
    title: string;
    engagement: number;
    reach: number;
    date: string;
  }>;
  engagementByType: Array<{
    type: string;
    likes: number;
    comments: number;
    shares: number;
  }>;
  bestTimes: Array<{
    day: string;
    hour: number;
    engagement: number;
  }>;
  monthlyData: Array<{
    month: string;
    posts: number;
    reach: number;
    engagement: number;
  }>;
}

type TimeRange = '7d' | '30d' | '90d' | '1y';

export default function Analytics() {
  const { user, loading } = useAuth();
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');
  const [isRefreshing, setIsRefreshing] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    );
  }

  // Dados fake para demonstração
  const analyticsData: AnalyticsData = {
    overview: {
      totalPosts: 24,
      totalReach: 45200,
      totalEngagement: 3840,
      avgEngagementRate: 8.5,
      followers: 1250,
      profileViews: 2890
    },
    trends: {
      postsGrowth: 12.5,
      reachGrowth: 23.8,
      engagementGrowth: 15.2,
      followersGrowth: 8.7
    },
    topPosts: [
      {
        id: '1',
        title: '🚀 Code2Post - Timeline Retroativa Implementada',
        engagement: 67,
        reach: 3200,
        date: '2025-01-26'
      },
      {
        id: '2',
        title: '⚙️ Configurando Backend com Node.js',
        engagement: 45,
        reach: 2100,
        date: '2025-01-25'
      },
      {
        id: '3',
        title: '🎨 Portfolio Redesign Completo',
        engagement: 31,
        reach: 1800,
        date: '2025-01-24'
      }
    ],
    engagementByType: [
      { type: 'milestone', likes: 245, comments: 48, shares: 23 },
      { type: 'feature', likes: 189, comments: 32, shares: 15 },
      { type: 'update', likes: 156, comments: 28, shares: 12 },
      { type: 'bugfix', likes: 98, comments: 18, shares: 8 }
    ],
    bestTimes: [
      { day: 'Segunda', hour: 9, engagement: 12.5 },
      { day: 'Terça', hour: 14, engagement: 15.2 },
      { day: 'Quarta', hour: 10, engagement: 11.8 },
      { day: 'Quinta', hour: 16, engagement: 14.7 },
      { day: 'Sexta', hour: 11, engagement: 13.1 }
    ],
    monthlyData: [
      { month: 'Set', posts: 8, reach: 12000, engagement: 980 },
      { month: 'Out', posts: 12, reach: 18500, engagement: 1420 },
      { month: 'Nov', posts: 15, reach: 24200, engagement: 1890 },
      { month: 'Dez', posts: 18, reach: 32100, engagement: 2650 },
      { month: 'Jan', posts: 24, reach: 45200, engagement: 3840 }
    ]
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsRefreshing(false);
  };

  const handleExport = () => {
    console.log('Exportar relatório');
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const getTrendIcon = (growth: number) => {
    return growth > 0 ? (
      <ArrowUp className="w-4 h-4 text-green-400" />
    ) : (
      <ArrowDown className="w-4 h-4 text-red-400" />
    );
  };

  const getTrendColor = (growth: number) => {
    return growth > 0 ? 'text-green-400' : 'text-red-400';
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Analytics
            </h1>
            <p className="text-slate-300 mt-2">
              Acompanhe o desempenho dos seus posts e engajamento
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-slate-400" />
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as TimeRange)}
                className="bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white text-sm"
              >
                <option value="7d">Últimos 7 dias</option>
                <option value="30d">Últimos 30 dias</option>
                <option value="90d">Últimos 90 dias</option>
                <option value="1y">Último ano</option>
              </select>
            </div>
            
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
            
            <Button
              onClick={handleExport}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Total de Posts</CardTitle>
                <BarChart3 className="w-5 h-5 text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {analyticsData.overview.totalPosts}
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(analyticsData.trends.postsGrowth)}
                <span className={`text-sm ${getTrendColor(analyticsData.trends.postsGrowth)}`}>
                  {Math.abs(analyticsData.trends.postsGrowth)}% vs período anterior
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Alcance Total</CardTitle>
                <Eye className="w-5 h-5 text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {formatNumber(analyticsData.overview.totalReach)}
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(analyticsData.trends.reachGrowth)}
                <span className={`text-sm ${getTrendColor(analyticsData.trends.reachGrowth)}`}>
                  {Math.abs(analyticsData.trends.reachGrowth)}% vs período anterior
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Engajamento</CardTitle>
                <ThumbsUp className="w-5 h-5 text-purple-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {formatNumber(analyticsData.overview.totalEngagement)}
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(analyticsData.trends.engagementGrowth)}
                <span className={`text-sm ${getTrendColor(analyticsData.trends.engagementGrowth)}`}>
                  {Math.abs(analyticsData.trends.engagementGrowth)}% vs período anterior
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Taxa de Engajamento</CardTitle>
                <Target className="w-5 h-5 text-orange-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {analyticsData.overview.avgEngagementRate}%
              </div>
              <div className="text-sm text-slate-400">
                Média de engajamento por post
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Seguidores</CardTitle>
                <Users className="w-5 h-5 text-cyan-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {formatNumber(analyticsData.overview.followers)}
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(analyticsData.trends.followersGrowth)}
                <span className={`text-sm ${getTrendColor(analyticsData.trends.followersGrowth)}`}>
                  {Math.abs(analyticsData.trends.followersGrowth)}% vs período anterior
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Visualizações do Perfil</CardTitle>
                <Eye className="w-5 h-5 text-yellow-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">
                {formatNumber(analyticsData.overview.profileViews)}
              </div>
              <div className="text-sm text-slate-400">
                Visualizações do seu perfil
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Performance */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-green-400" />
                Performance Mensal
              </CardTitle>
              <CardDescription className="text-slate-300">
                Evolução de posts, alcance e engajamento
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.monthlyData.map((data, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">{data.month}</span>
                      </div>
                      <div>
                        <div className="text-white font-medium">{data.posts} posts</div>
                        <div className="text-slate-400 text-sm">
                          {formatNumber(data.reach)} alcance • {formatNumber(data.engagement)} engajamento
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 text-sm font-medium">
                        +{((data.engagement / data.reach) * 100).toFixed(1)}%
                      </div>
                      <div className="text-slate-400 text-xs">taxa eng.</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Posts */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Award className="w-5 h-5 mr-2 text-yellow-400" />
                Top Posts
              </CardTitle>
              <CardDescription className="text-slate-300">
                Posts com melhor performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topPosts.map((post, index) => (
                  <div key={post.id} className="flex items-center space-x-3 p-3 bg-slate-700/30 rounded-lg">
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">#{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="text-white font-medium text-sm line-clamp-1">
                        {post.title}
                      </div>
                      <div className="text-slate-400 text-xs">
                        {new Date(post.date).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 font-medium">{post.engagement}</div>
                      <div className="text-slate-400 text-xs">engajamento</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Engagement by Type & Best Times */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Engagement by Type */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-blue-400" />
                Engajamento por Tipo
              </CardTitle>
              <CardDescription className="text-slate-300">
                Performance por categoria de post
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.engagementByType.map((type, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white capitalize">{type.type}</span>
                      <span className="text-slate-400 text-sm">
                        {type.likes + type.comments + type.shares} total
                      </span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-blue-500/20 rounded p-2 text-center">
                        <div className="text-blue-400 font-medium">{type.likes}</div>
                        <div className="text-xs text-slate-400">Curtidas</div>
                      </div>
                      <div className="bg-green-500/20 rounded p-2 text-center">
                        <div className="text-green-400 font-medium">{type.comments}</div>
                        <div className="text-xs text-slate-400">Comentários</div>
                      </div>
                      <div className="bg-purple-500/20 rounded p-2 text-center">
                        <div className="text-purple-400 font-medium">{type.shares}</div>
                        <div className="text-xs text-slate-400">Compartilhamentos</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Best Times */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Clock className="w-5 h-5 mr-2 text-purple-400" />
                Melhores Horários
              </CardTitle>
              <CardDescription className="text-slate-300">
                Quando seus posts têm melhor performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analyticsData.bestTimes.map((time, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-purple-400" />
                      <span className="text-white">{time.day}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className="text-slate-300">{time.hour}:00</span>
                      <div className="text-right">
                        <div className="text-green-400 font-medium">{time.engagement}%</div>
                        <div className="text-slate-400 text-xs">engajamento</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-3 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
                <div className="flex items-center">
                  <Zap className="w-4 h-4 text-purple-400 mr-2" />
                  <span className="text-sm text-purple-400 font-medium">
                    Melhor horário: Terça-feira às 14:00 (15.2% de engajamento)
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
