import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Github, Mail, Lock, User, Sparkles, Code, Zap, ArrowLeft, Check, X } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { isApiError } from '@/types/api';

// Função utilitária para obter a URL da API
const getApiUrl = () => {
  if (window.location.hostname === 'www.code2post.com' || window.location.hostname === 'code2post.com') {
    return 'https://api.code2post.com';
  }
  return import.meta.env.VITE_API_URL || 'http://localhost:3001';
};

export default function Register() {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [githubLoading, setGithubLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  // Validação de senha em tempo real
  const passwordRequirements = {
    minLength: formData.password.length >= 8,
    hasLowercase: /[a-z]/.test(formData.password),
    hasUppercase: /[A-Z]/.test(formData.password),
    hasNumber: /\d/.test(formData.password),
    hasSymbol: /[@$!%*?&]/.test(formData.password),
    passwordsMatch: formData.password === formData.confirmPassword && formData.confirmPassword !== ''
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validações básicas
    if (formData.password !== formData.confirmPassword) {
      toast.error('As senhas não coincidem');
      return;
    }

    if (formData.password.length < 8) {
      toast.error('A senha deve ter pelo menos 8 caracteres');
      return;
    }

    setLoading(true);

    try {
      const apiUrl = getApiUrl();
      const response = await axios.post(`${apiUrl}/auth/register`, {
        name: formData.name,
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
      });

      // Usar o contexto para fazer login
      login(response.data.accessToken, response.data.refreshToken, response.data.user);

      toast.success('Conta criada com sucesso!');
      navigate('/dashboard');
    } catch (error: unknown) {
      console.error('❌ Erro no registro:', error);
      
      if (isApiError(error) && error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('Erro ao criar conta. Tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGitHubLogin = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    try {
      setGithubLoading(true);

      const apiUrl = getApiUrl();
      const response = await fetch(`${apiUrl}/auth/github/login`);
      const data = await response.json();
      
      if (!data.authUrl) {
        throw new Error('Erro ao obter URL de autorização');
      }
      
      window.location.href = data.authUrl;
      
    } catch (error) {
      console.error('Erro no login GitHub:', error);
      toast.error('Erro ao conectar com GitHub. Tente novamente.');
    } finally {
      setGithubLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
          <Code size={24} />
        </div>
        <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
          <Sparkles size={24} />
        </div>
        <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
          <Zap size={24} />
        </div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-12 h-12 flex items-center justify-center">
                <img src="/svg/icon-48.svg" alt="Code2Post" className="w-12 h-12" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Code2Post
              </h1>
            </div>
            <p className="text-slate-300 text-lg">
              Crie sua conta e comece a compartilhar
            </p>
            <p className="text-slate-400 text-sm">
              Conecte seu GitHub e gere posts profissionais com IA
            </p>
          </div>

          {/* Register Card */}
          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader className="text-center space-y-2">
              <CardTitle className="text-xl text-white">Criar conta</CardTitle>
              <CardDescription className="text-slate-300">
                Preencha os dados para começar
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* GitHub Login Button */}
              <Button 
                onClick={handleGitHubLogin} 
                className="w-full h-12 bg-slate-700 hover:bg-slate-600 border-slate-600 hover:border-slate-500 text-white transition-all duration-300 group"
                disabled={githubLoading}
              >
                {githubLoading ? (
                  <>
                    <div className="w-5 h-5 mr-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Conectando com GitHub...
                  </>
                ) : (
                  <>
                    <Github className="w-5 h-5 mr-3 group-hover:scale-110 transition-transform" />
                    Continuar com GitHub
                  </>
                )}
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-slate-600/50" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-slate-800/50 px-3 text-slate-400 backdrop-blur-sm">ou</span>
                </div>
              </div>

              {/* Registration Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <Input
                      id="name"
                      type="text"
                      name="name"
                      placeholder="Seu nome completo"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="pl-10 h-12 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:ring-blue-500/20"
                      autoComplete="name"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <Input
                      id="email"
                      type="email"
                      name="email"
                      placeholder="Seu email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="pl-10 h-12 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:ring-blue-500/20"
                      autoComplete="email"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <Input
                      id="password"
                      type="password"
                      name="password"
                      placeholder="Sua senha"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="pl-10 h-12 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:ring-blue-500/20"
                      autoComplete="new-password"
                      required
                    />
                  </div>
                  
                  {/* Password Requirements */}
                  {formData.password && (
                    <div className="mt-3 p-3 bg-slate-700/30 rounded-lg border border-slate-600/30">
                      <p className="text-xs text-slate-300 mb-2 font-medium">Requisitos da senha:</p>
                      <div className="space-y-1">
                        <div className={`flex items-center text-xs ${passwordRequirements.minLength ? 'text-blue-400' : 'text-slate-400'}`}>
                          {passwordRequirements.minLength ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                          Pelo menos 8 caracteres
                        </div>
                        <div className={`flex items-center text-xs ${passwordRequirements.hasLowercase ? 'text-blue-400' : 'text-slate-400'}`}>
                          {passwordRequirements.hasLowercase ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                          Uma letra minúscula
                        </div>
                        <div className={`flex items-center text-xs ${passwordRequirements.hasUppercase ? 'text-blue-400' : 'text-slate-400'}`}>
                          {passwordRequirements.hasUppercase ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                          Uma letra maiúscula
                        </div>
                        <div className={`flex items-center text-xs ${passwordRequirements.hasNumber ? 'text-blue-400' : 'text-slate-400'}`}>
                          {passwordRequirements.hasNumber ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                          Um número
                        </div>
                        <div className={`flex items-center text-xs ${passwordRequirements.hasSymbol ? 'text-blue-400' : 'text-slate-400'}`}>
                          {passwordRequirements.hasSymbol ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                          Um símbolo (@$!%*?&)
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <Input
                      id="confirmPassword"
                      type="password"
                      name="confirmPassword"
                      placeholder="Confirme sua senha"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      autoComplete="new-password"
                      className={`pl-10 h-12 bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-500/50 focus:ring-blue-500/20 ${
                        formData.confirmPassword && !passwordRequirements.passwordsMatch ? 'border-red-500/50' : ''
                      }`}
                      required
                    />
                  </div>
                  
                  {/* Password Match Indicator */}
                  {formData.confirmPassword && (
                    <div className={`flex items-center text-xs ${passwordRequirements.passwordsMatch ? 'text-blue-400' : 'text-red-400'}`}>
                      {passwordRequirements.passwordsMatch ? <Check className="w-3 h-3 mr-2" /> : <X className="w-3 h-3 mr-2" />}
                      {passwordRequirements.passwordsMatch ? 'Senhas coincidem' : 'Senhas não coincidem'}
                    </div>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium transition-all duration-300 transform hover:scale-[1.02]" 
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      Criando conta...
                    </>
                  ) : (
                    'Criar conta'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center space-y-4">
            <p className="text-slate-400 text-sm">
              Já tem uma conta?{' '}
              <Link to="/login" className="text-blue-400 hover:text-blue-300 transition-colors">
                Fazer login
              </Link>
            </p>
            <p className="text-slate-500 text-xs">
              Ao criar uma conta, você concorda com nossos{' '} <br></br>
              <Link to="/terms-of-service" className="text-slate-400 hover:text-slate-300 transition-colors">
                Termos de Serviço
              </Link>
              {' • '}
              <Link to="/privacy-policy" className="text-slate-400 hover:text-slate-300 transition-colors">
                Política de Privacidade
              </Link>
              {' • '}
              <Link to="/cookie-policy" className="text-slate-400 hover:text-slate-300 transition-colors">
                Política de Cookies
              </Link>
            </p>
          </div>

          {/* Back to Login */}
          <div className="text-center">
            <Link 
              to="/" 
              className="inline-flex items-center text-slate-400 hover:text-slate-300 transition-colors group"
            >
              <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              Voltar ao início
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 
