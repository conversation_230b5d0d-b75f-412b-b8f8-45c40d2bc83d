import { Link } from 'react-router-dom';

interface PolicyFooterProps {
  currentPage?: 'privacy' | 'terms' | 'cookies';
}

export function PolicyFooter({ currentPage }: PolicyFooterProps) {
  const links = [
    { key: 'privacy', to: '/privacy-policy', label: 'Política de Privacidade' },
    { key: 'terms', to: '/terms-of-service', label: 'Termos de Serviço' },
    { key: 'cookies', to: '/cookie-policy', label: 'Política de Cookies' }
  ];

  const filteredLinks = links.filter(link => link.key !== currentPage);

  return (
    <div className="text-center space-y-4 pb-8">
      <p className="text-slate-400 text-sm">
        {filteredLinks.map((link, index) => (
          <span key={link.key}>
            <Link 
              to={link.to} 
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              {link.label}
            </Link>
            {index < filteredLinks.length - 1 && ' • '}
          </span>
        ))}
      </p>
    </div>
  );
}
