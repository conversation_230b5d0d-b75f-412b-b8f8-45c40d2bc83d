import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs'; // Importa o bcryptjs para hash seguro
import { body, validationResult, header } from 'express-validator'; // Importa express-validator
import { authenticateToken, atualizarBlacklist } from '../middleware/auth.js';
import {
  detectSuspiciousLogin,
  getUserLoginStats,
} from '../middleware/suspiciousLogin.js';
import userService from '../services/userService.js';

const router = express.Router();

// Middleware para tratar erros de validação
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
      })),
    });
  }
  next();
};

// Lista de senhas comuns (em produção, seria um arquivo separado)
const senhasComuns = [
  '123456',
  'password',
  '123456789',
  '12345678',
  '12345',
  'qwerty',
  'abc123',
  'football',
  '1234567',
  'monkey',
  '111111',
  'letmein',
  '1234',
  '1234567890',
  'dragon',
  'baseball',
  'sunshine',
  'iloveyou',
  'trustno1',
  'princess',
  'admin',
  'welcome',
  'solo',
  'master',
  'hello',
  'freedom',
  'whatever',
  'qazwsx',
  'ninja',
  'michael',
];

// Função para verificar se senha contém dados pessoais
const verificarDadosPessoais = (senha, nome, email) => {
  const nomeLimpo = nome.toLowerCase().replace(/[^a-z]/g, '');
  const emailLimpo = email.toLowerCase().split('@')[0];

  return (
    senha.toLowerCase().includes(nomeLimpo) ||
    senha.toLowerCase().includes(emailLimpo)
  );
};

// Validações para login
const validarLogin = [
  body('email')
    .isEmail()
    .withMessage('Email deve ter formato válido')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email muito longo'),

  body('password')
    .isLength({ min: 8 })
    .withMessage('Senha deve ter pelo menos 8 caracteres')
    .isLength({ max: 128 })
    .withMessage('Senha muito longa')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage(
      'Senha deve conter: 1 minúscula, 1 maiúscula, 1 número e 1 símbolo (@$!%*?&)'
    )
    .custom(value => {
      // Verificar se não é uma senha comum
      if (senhasComuns.includes(value.toLowerCase())) {
        throw new Error('Senha muito comum, escolha uma senha mais segura');
      }

      // Verificar se não contém dados pessoais (apenas se temos os dados)
      if (req.body.email) {
        const user = users.find(u => u.email === req.body.email);
        if (user && verificarDadosPessoais(value, user.name, user.email)) {
          throw new Error('Senha não pode conter seu nome ou email');
        }
      }

      return true;
    }),

  handleValidationErrors,
];

// Validações para refresh token
const validarRefresh = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token é obrigatório')
    .isJWT()
    .withMessage('Refresh token deve ter formato JWT válido'),

  handleValidationErrors,
];

// Validações para logout com refresh token
const validarLogoutAll = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token é obrigatório')
    .isJWT()
    .withMessage('Refresh token deve ter formato JWT válido'),

  handleValidationErrors,
];

// Validações para headers de autorização
const validarAuthHeader = [
  header('authorization')
    .notEmpty()
    .withMessage('Header Authorization é obrigatório')
    .matches(/^Bearer\s+/)
    .withMessage('Header Authorization deve começar com "Bearer "'),

  handleValidationErrors,
];

// Blacklist de tokens (em produção, seria um banco de dados)
const tokenBlacklist = new Set();

// Compartilhar blacklist com o middleware
atualizarBlacklist(tokenBlacklist);

// Função para limpar tokens expirados da blacklist
const limparBlacklist = () => {
  const agora = Date.now();
  for (const token of tokenBlacklist) {
    try {
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp * 1000 < agora) {
        tokenBlacklist.delete(token);
      }
    } catch (error) {
      // Token inválido, remover da blacklist
      tokenBlacklist.delete(token);
    }
  }
};

// Limpar blacklist a cada 1 hora
setInterval(limparBlacklist, 60 * 60 * 1000);

// Middleware para verificar se token está na blacklist
const verificarBlacklist = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token && tokenBlacklist.has(token)) {
    return res.status(401).json({ error: 'Token revogado' });
  }

  next();
};

// Rota de login com detecção de login suspeito
router.post('/login', validarLogin, detectSuspiciousLogin, async (req, res) => {
  try {
    const { email, password } = req.body;

    // Verificar credenciais usando o serviço
    const result = await userService.verifyCredentials(email, password);

    if (!result.success) {
      return res.status(401).json({ error: result.error });
    }

    const user = result.user;

    // Gerar token JWT
    const accessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        name: user.name,
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' } // Access token expira em 15 minutos
    );

    // Gerar refresh token
    const refreshToken = jwt.sign(
      {
        userId: user.id,
        type: 'refresh', // Identifica que é um refresh token
      },
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, // Usa secret diferente se disponível
      { expiresIn: '7d' } // Refresh token expira em 7 dias
    );

    // Verificar se foi detectado login suspeito
    const suspiciousLoginInfo = req.suspiciousLogin;

    console.log(`✅ Login realizado com sucesso: ${user.email}`);

    // Retornar tokens e dados do usuário
    res.json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        githubId: user.githubId,
        githubUsername: user.githubUsername,
        emailVerified: user.emailVerified,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        preferences: user.preferences,
      },
      // Incluir informação sobre login suspeito se detectado
      ...(suspiciousLoginInfo && {
        suspiciousLogin: {
          detected: true,
          reason: suspiciousLoginInfo.reason,
          timestamp: suspiciousLoginInfo.timestamp,
          device: suspiciousLoginInfo.deviceInfo,
        },
      }),
    });
  } catch (error) {
    console.error('❌ Erro no login:', error.message);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para verificar token
router.get(
  '/verify',
  validarAuthHeader,
  verificarBlacklist,
  authenticateToken,
  (req, res) => {
    res.json({
      message: 'Token válido',
      user: req.user,
    });
  }
);

// Rota para renovar access token usando refresh token
router.post('/refresh', validarRefresh, async (req, res) => {
  const { refreshToken } = req.body;

  try {
    // Verificar se o refresh token é válido
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET
    );

    // Verificar se é realmente um refresh token
    if (decoded.type !== 'refresh') {
      return res.status(403).json({ error: 'Token inválido' });
    }

    // Buscar usuário usando o serviço
    const user = userService.getUserById(decoded.userId);
    if (!user) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    // Gerar novo access token
    const newAccessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        name: user.name,
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );

    res.json({
      accessToken: newAccessToken,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        githubId: user.githubId,
        githubUsername: user.githubUsername,
        emailVerified: user.emailVerified,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        preferences: user.preferences,
      },
    });
  } catch (error) {
    return res.status(403).json({ error: 'Refresh token inválido' });
  }
});

// Rota para logout seguro
router.post(
  '/logout',
  validarAuthHeader,
  verificarBlacklist,
  authenticateToken,
  (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      // Adicionar token à blacklist
      tokenBlacklist.add(token);
    }

    res.json({ message: 'Logout realizado com sucesso' });
  }
);

// Rota para logout com refresh token (logout de todos os dispositivos)
router.post('/logout-all', validarLogoutAll, async (req, res) => {
  const { refreshToken } = req.body;

  try {
    // Verificar se o refresh token é válido
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET
    );

    // Adicionar refresh token à blacklist
    tokenBlacklist.add(refreshToken);

    res.json({
      message: 'Logout de todos os dispositivos realizado com sucesso',
    });
  } catch (error) {
    return res.status(403).json({ error: 'Refresh token inválido' });
  }
});

// Rota para obter estatísticas de login do usuário
router.get('/login-stats', authenticateToken, (req, res) => {
  try {
    const userEmail = req.user.email;
    const loginStats = getUserLoginStats(userEmail);

    if (!loginStats) {
      return res.status(404).json({
        error: 'Estatísticas de login não encontradas',
      });
    }

    // Retornar estatísticas sem informações sensíveis
    res.json({
      loginCount: loginStats.loginCount,
      lastLogin: loginStats.lastLogin,
      lastDevice: loginStats.lastDevice,
      suspiciousLoginsCount: loginStats.suspiciousLogins.length,
      suspiciousLogins: loginStats.suspiciousLogins.map(login => ({
        timestamp: login.timestamp,
        reason: login.reason,
        device: login.deviceInfo,
      })),
    });
  } catch (error) {
    console.error('Erro ao obter estatísticas de login:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
    });
  }
});

export default router;
