import axios from 'axios';
import { toast } from 'sonner';

// Configuração base do Axios
const getApiUrl = () => {
  // Em produção, sempre usar a URL de produção
  if (window.location.hostname === 'www.code2post.com' || window.location.hostname === 'code2post.com') {
    return 'https://api.code2post.com';
  }
  // Em desenvolvimento, usar variável de ambiente ou localhost
  return import.meta.env.VITE_API_URL || 'http://localhost:3001';
};

const apiUrl = getApiUrl();

const api = axios.create({
  baseURL: apiUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para requisições
api.interceptors.request.use(
  (config) => {
    // Adicionar token de autenticação se existir
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para respostas
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 - Token expirado (COMENTADO PARA DEBUG)
    if (error.response?.status === 401) {
      console.error('🔍 ERRO 401 DETECTADO:', error.response.data);
      console.error('🔍 URL da requisição:', error.config?.url);
      console.error('🔍 Headers enviados:', error.config?.headers);
      
      // NÃO redirecionar automaticamente para debug
      // localStorage.removeItem('accessToken');
      // localStorage.removeItem('refreshToken');
      // localStorage.removeItem('user');
      // window.location.href = '/login';
      // toast.error('Sessão expirada. Faça login novamente.');
      
      toast.error(`Erro 401: ${error.response.data?.error || 'Não autorizado'}`);
      return Promise.reject(error);
    }

    // Handle outros erros
    const message = error.response?.data?.message || 'Erro na requisição';
    toast.error(message);
    
    return Promise.reject(error);
  }
);

export default api;
