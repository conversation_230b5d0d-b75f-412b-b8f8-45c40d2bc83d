import { useEffect } from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

export default function SEO({
  title = 'Code2Post - Transforme Sua Atividade GitHub em Conteúdo LinkedIn',
  description = 'Gere automaticamente posts envolventes para o LinkedIn a partir da sua atividade no GitHub. Deixe a IA mostrar sua jornada de desenvolvimento e expanda sua rede profissional. Comece grátis hoje!',
  keywords = 'github, linkedin, ia, geração de conteúdo, ferramentas para desenvolvedores, automação de redes sociais, programação, código, posts, networking profissional, brasil',
  image = '/og-image.jpg',
  url = 'https://code2post.com',
  type = 'website'
}: SEOProps) {
  const fullTitle = title.includes('Code2Post') ? title : `${title} | Code2Post`;

  useEffect(() => {
    // Update document title
    document.title = fullTitle;

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;

      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }

      meta.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', 'Code2Post');

    // Open Graph meta tags
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:title', fullTitle, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:url', url, true);
    updateMetaTag('og:site_name', 'Code2Post', true);

    // Twitter meta tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', image);

  }, [fullTitle, description, keywords, image, url, type]);

  return null; // This component doesn't render anything
}
