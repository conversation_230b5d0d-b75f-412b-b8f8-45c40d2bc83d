import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { type LucideIcon } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  gradient: {
    from: string;
    to: string;
  };
  glowColor: string;
}

export default function MetricCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend
}: MetricCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      className={`
        relative overflow-hidden transition-all duration-500 transform cursor-pointer backdrop-blur-xl
        ${isHovered
          ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
          : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
        }
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* <PERSON><PERSON><PERSON> de brilho */}
      <div className={`
        absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10
        transition-opacity duration-500 ${isHovered ? 'opacity-100' : 'opacity-0'}
      `} />

      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
        <CardTitle className={`
          text-sm font-medium transition-colors duration-300
          ${isHovered ? 'text-blue-300' : 'text-slate-300'}
        `}>
          {title}
        </CardTitle>
        <div className="relative">
          <Icon className={`
            w-5 h-5 transition-all duration-300
            ${isHovered ? 'text-blue-400 scale-110' : 'text-blue-500'}
          `} />
          {isHovered && (
            <div className="absolute inset-0 w-5 h-5 bg-blue-400 rounded-full animate-ping opacity-30" />
          )}
        </div>
      </CardHeader>

      <CardContent className="relative z-10">
        <div className={`
          text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400
          bg-clip-text text-transparent mb-1 transition-all duration-300
          ${isHovered ? 'scale-110' : ''}
        `}>
          {value}
        </div>
        
        <div className="flex items-center justify-between">
          <p className={`
            text-xs transition-colors duration-300
            ${isHovered ? 'text-slate-200' : 'text-slate-400'}
          `}>
            {subtitle}
          </p>
          
          {trend && (
            <div className={`
              flex items-center text-xs font-medium
              ${trend.isPositive ? 'text-green-400' : 'text-red-400'}
            `}>
              <span className="mr-1">
                {trend.isPositive ? '↗' : '↘'}
              </span>
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
