import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Clock,
  Mail,
  Github,
  Linkedin,
  <PERSON>,
  <PERSON>ch,
  <PERSON>rk<PERSON>,
  <PERSON>,
  Heart
} from 'lucide-react';

interface MaintenanceModalProps {
  isOpen: boolean;
  onClose?: () => void;
}

export default function MaintenanceModal({ isOpen }: MaintenanceModalProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop with blur */}
      <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />
      
      {/* Modal */}
      <Card className="relative w-full max-w-2xl bg-gradient-to-br from-slate-900/95 via-purple-900/20 to-slate-900/95 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                <Wrench className="w-10 h-10 text-white animate-pulse" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
          
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            🚧 Sistema em Manutenção
          </CardTitle>
          
          <CardDescription className="text-slate-300 text-lg mt-2">
            Estamos trabalhando para trazer uma experiência ainda melhor para você!
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Main Message */}
          <div className="text-center space-y-4">
            <p className="text-slate-200 leading-relaxed">
              Olá! 👋 Estamos realizando algumas melhorias importantes no <span className="font-semibold text-purple-400">Code2Post</span> para oferecer uma experiência ainda mais incrível.
            </p>
            
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Clock className="w-5 h-5 text-blue-400" />
                <span className="text-blue-300 font-medium">Previsão de Lançamento (beta)</span>
              </div>
              <p className="text-white text-lg font-semibold">
                20 de Agosto de 2025 ⏰
              </p>
            </div>
          </div>

          {/* What we're working on */}
          {!showDetails ? (
            <div className="text-center">
              <Button
                onClick={() => setShowDetails(true)}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
              >
                <Rocket className="w-4 h-4 mr-2" />
                Ver o que estamos melhorando
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <h3 className="text-white font-semibold text-center flex items-center justify-center">
                <Rocket className="w-5 h-5 mr-2 text-purple-400" />
                Novidades que estão chegando:
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="bg-slate-800/30 rounded-lg p-3 border border-slate-700/50">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-green-400 text-sm font-medium">Interface</span>
                  </div>
                  <p className="text-slate-300 text-sm">Dashboard mais intuitivo e responsivo</p>
                </div>
                
                <div className="bg-slate-800/30 rounded-lg p-3 border border-slate-700/50">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-blue-400 text-sm font-medium">Performance</span>
                  </div>
                  <p className="text-slate-300 text-sm">Carregamento mais rápido e otimizado</p>
                </div>
                
                <div className="bg-slate-800/30 rounded-lg p-3 border border-slate-700/50">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <span className="text-purple-400 text-sm font-medium">Funcionalidades</span>
                  </div>
                  <p className="text-slate-300 text-sm">Novos recursos de automação</p>
                </div>
                
                <div className="bg-slate-800/30 rounded-lg p-3 border border-slate-700/50">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-yellow-400 text-sm font-medium">Segurança</span>
                  </div>
                  <p className="text-slate-300 text-sm">Melhorias na proteção de dados</p>
                </div>
              </div>
              
              <div className="text-center">
                <Button
                  onClick={() => setShowDetails(false)}
                  variant="ghost"
                  size="sm"
                  className="text-slate-400 hover:text-white"
                >
                  <X className="w-4 h-4 mr-1" />
                  Fechar detalhes
                </Button>
              </div>
            </div>
          )}

          {/* Contact Info */}
          <div className="border-t border-slate-700/50 pt-6">
            <div className="text-center space-y-4">
              <p className="text-slate-300 text-sm">
                Precisa falar conosco? Entre em contato:
              </p>
              
              <div className="flex justify-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Email
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  onClick={() => window.open('https://github.com/gabrielcamarate', '_blank')}
                >
                  <Github className="w-4 h-4 mr-2" />
                  GitHub
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  onClick={() => window.open('https://linkedin.com/in/gabriel-camarate', '_blank')}
                >
                  <Linkedin className="w-4 h-4 mr-2" />
                  LinkedIn
                </Button>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center pt-4 border-t border-slate-700/50">
            <p className="text-slate-400 text-sm flex items-center justify-center">
              Feito com <Heart className="w-4 h-4 mx-1 text-red-400" /> pela equipe Code2Post
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
