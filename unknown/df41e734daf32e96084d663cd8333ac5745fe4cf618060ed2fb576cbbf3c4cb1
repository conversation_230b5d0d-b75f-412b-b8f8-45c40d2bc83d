import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import MetricCard from '@/components/dashboard/cards/MetricCard';
import QuickActions from '@/components/dashboard/ui/QuickActions';
import ActivityFeed from '@/components/dashboard/ui/ActivityFeed';
import RepositorySelector from '@/components/dashboard/sections/RepositorySelector';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FileText,
  FolderGit2,
  TrendingUp,
  Users,
  Sparkles,
  GitBranch,
  Calendar,
  Clock
} from 'lucide-react';

export default function Dashboard() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    );
  }

  // Dados fake para demonstração
  const metrics = [
    {
      title: 'Posts Gerados',
      value: '24',
      subtitle: 'Este mês',
      icon: FileText,
      trend: { value: 12, isPositive: true },
      gradient: { from: 'blue', to: 'purple' },
      glowColor: 'blue'
    },
    {
      title: 'Repositórios',
      value: '8',
      subtitle: 'Conectados',
      icon: FolderGit2,
      trend: { value: 2, isPositive: true },
      gradient: { from: 'purple', to: 'pink' },
      glowColor: 'purple'
    },
    {
      title: 'Engajamento',
      value: '94%',
      subtitle: 'Taxa média',
      icon: TrendingUp,
      trend: { value: 8, isPositive: true },
      gradient: { from: 'green', to: 'cyan' },
      glowColor: 'green'
    },
    {
      title: 'Seguidores',
      value: '1.2K',
      subtitle: 'LinkedIn',
      icon: Users,
      trend: { value: 15, isPositive: true },
      gradient: { from: 'orange', to: 'red' },
      glowColor: 'orange'
    }
  ];

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Bem-vindo de volta, {user?.name?.split(' ')[0]}!
              </h1>
              <p className="text-slate-300">
                Vamos transformar seu código em conteúdo incrível hoje
              </p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <QuickActions />

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <MetricCard
              key={index}
              title={metric.title}
              value={metric.value}
              subtitle={metric.subtitle}
              icon={metric.icon}
              trend={metric.trend}
              gradient={metric.gradient}
              glowColor={metric.glowColor}
            />
          ))}
        </div>

        {/* Repository Selector */}
        <RepositorySelector />

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Activity Feed - 2/3 width */}
          <div className="lg:col-span-2">
            <ActivityFeed />
          </div>

          {/* Side Panel - 1/3 width */}
          <div className="space-y-6">
            {/* Recent Commits */}
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <GitBranch className="w-5 h-5 mr-2 text-blue-400" />
                  Commits Recentes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { message: 'feat: add dashboard redesign', time: '2h', repo: 'Code2Post' },
                    { message: 'fix: resolve auth issues', time: '4h', repo: 'Code2Post' },
                    { message: 'docs: update README', time: '1d', repo: 'Portfolio' }
                  ].map((commit, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 bg-slate-700/30 rounded">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-white truncate">{commit.message}</p>
                        <p className="text-xs text-slate-400">{commit.repo} • {commit.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Posts */}
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-purple-400" />
                  Posts Agendados
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { title: 'Dashboard Redesign Progress', time: 'Hoje 14:00' },
                    { title: 'New Authentication System', time: 'Amanhã 09:00' },
                    { title: 'Weekly Development Update', time: 'Sex 16:00' }
                  ].map((post, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 bg-slate-700/30 rounded">
                      <Clock className="w-4 h-4 text-blue-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-white truncate">{post.title}</p>
                        <p className="text-xs text-slate-400">{post.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}