import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import TimelineAnalyzer from '@/components/dashboard/timeline/TimelineAnalyzer';
import TimelinePlanner from '@/components/dashboard/timeline/TimelinePlanner';
import PostPreview from '@/components/dashboard/timeline/PostPreview';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  GitBranch, 
  Sparkles, 
  ArrowRight, 
  CheckCircle,
  Circle,
  Play,
  RotateCcw
} from 'lucide-react';

interface Repository {
  id: string;
  name: string;
  fullName: string;
  description: string;
  isConnected: boolean;
  totalCommits: number;
  lastActivity: string;
}

type TimelineStep = 'select' | 'analyze' | 'plan' | 'preview' | 'activate';

export default function Timeline() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<TimelineStep>('select');
  const [selectedRepository, setSelectedRepository] = useState<Repository | null>(null);
  const [analysisData, setAnalysisData] = useState<any>(null);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    );
  }

  // Dados fake de repositórios para demonstração
  const repositories: Repository[] = [
    {
      id: '1',
      name: 'Code2Post',
      fullName: 'gabrielcamarate/Code2Post',
      description: 'Automatize e compartilhe seu progresso no LinkedIn, diretamente do GitHub, com IA.',
      isConnected: true,
      totalCommits: 87,
      lastActivity: '2 horas atrás'
    },
    {
      id: '2',
      name: 'Portfolio',
      fullName: 'gabrielcamarate/Portfolio',
      description: 'Meu portfólio pessoal desenvolvido com React e Next.js',
      isConnected: true,
      totalCommits: 45,
      lastActivity: '1 dia atrás'
    },
    {
      id: '3',
      name: 'TaskManager',
      fullName: 'gabrielcamarate/TaskManager',
      description: 'Sistema de gerenciamento de tarefas com React e Node.js',
      isConnected: false,
      totalCommits: 62,
      lastActivity: '3 dias atrás'
    }
  ];

  const steps = [
    { id: 'select', title: 'Selecionar Repositório', description: 'Escolha o projeto para análise' },
    { id: 'analyze', title: 'Análise Inteligente', description: 'IA processa histórico de commits' },
    { id: 'plan', title: 'Planejar Timeline', description: 'Configure posts e frequência' },
    { id: 'preview', title: 'Preview dos Posts', description: 'Revise conteúdo antes de publicar' },
    { id: 'activate', title: 'Ativar Timeline', description: 'Lance sua timeline retroativa' }
  ];

  const getStepStatus = (stepId: string) => {
    const stepIndex = steps.findIndex(s => s.id === stepId);
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'upcoming';
  };

  const handleRepositorySelect = (repo: Repository) => {
    setSelectedRepository(repo);
    setCurrentStep('analyze');
  };

  const handleAnalysisComplete = (analysis: any) => {
    setAnalysisData(analysis);
    setCurrentStep('plan');
  };

  const handlePlanningComplete = (config: any, posts: any[]) => {
    console.log('Timeline configurada:', { config, posts });
    setCurrentStep('preview');
  };

  const resetTimeline = () => {
    setCurrentStep('select');
    setSelectedRepository(null);
    setAnalysisData(null);
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                Timeline Retroativa
              </h1>
              <p className="text-slate-300 mt-2">
                Transforme projetos prontos em uma narrativa envolvente de desenvolvimento
              </p>
            </div>
            
            {currentStep !== 'select' && (
              <Button
                onClick={resetTimeline}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Recomeçar
              </Button>
            )}
          </div>
        </div>

        {/* Progress Steps */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Clock className="w-5 h-5 mr-2 text-purple-400" />
              Progresso da Timeline
            </CardTitle>
          </CardHeader>
          <CardContent className="pb-4">
            <div className="flex items-start justify-between">
              {steps.map((step, index) => {
                const status = getStepStatus(step.id);
                return (
                  <div key={step.id} className="flex items-start">
                    <div className="flex flex-col items-center min-w-0">
                      <div className={`
                        w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300
                        ${status === 'completed' 
                          ? 'bg-green-500 border-green-500 text-white' 
                          : status === 'current'
                          ? 'bg-purple-500 border-purple-500 text-white'
                          : 'bg-slate-700 border-slate-600 text-slate-400'
                        }
                      `}>
                        {status === 'completed' ? (
                          <CheckCircle className="w-5 h-5" />
                        ) : status === 'current' ? (
                          <Play className="w-5 h-5" />
                        ) : (
                          <Circle className="w-5 h-5" />
                        )}
                      </div>
                      <div className="mt-3 text-center">
                        <div className={`text-sm font-medium leading-tight ${
                          status === 'current' ? 'text-purple-400' :
                          status === 'completed' ? 'text-green-400' : 'text-slate-400'
                        }`}>
                          {step.title}
                        </div>
                        <div className="text-xs text-slate-500 mt-1 leading-tight max-w-28 mx-auto">
                          {step.description}
                        </div>
                      </div>
                    </div>

                    {index < steps.length - 1 && (
                      <ArrowRight className={`w-5 h-5 mx-3 mt-5 ${
                        getStepStatus(steps[index + 1].id) === 'completed' ? 'text-green-400' : 'text-slate-600'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        {currentStep === 'select' && (
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <GitBranch className="w-5 h-5 mr-2 text-blue-400" />
                Selecionar Repositório
              </CardTitle>
              <CardDescription className="text-slate-300">
                Escolha o repositório que deseja transformar em timeline retroativa
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {repositories.map((repo) => (
                  <div
                    key={repo.id}
                    onClick={() => handleRepositorySelect(repo)}
                    className={`
                      p-4 rounded-lg border cursor-pointer transition-all duration-200
                      ${repo.isConnected
                        ? 'bg-slate-700/30 border-slate-600/30 hover:bg-slate-700/50 hover:border-blue-500/50'
                        : 'bg-slate-700/10 border-slate-600/20 opacity-60 cursor-not-allowed'
                      }
                    `}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-white font-medium">{repo.name}</h3>
                      {repo.isConnected ? (
                        <Badge className="bg-green-500/10 text-green-400 border-green-500/30">
                          Conectado
                        </Badge>
                      ) : (
                        <Badge className="bg-red-500/10 text-red-400 border-red-500/30">
                          Desconectado
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-slate-300 text-sm mb-3 line-clamp-2">
                      {repo.description}
                    </p>

                    <div className="flex items-center justify-between text-xs text-slate-400">
                      <span>{repo.totalCommits} commits</span>
                      <span>{repo.lastActivity}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {currentStep === 'analyze' && selectedRepository && (
          <TimelineAnalyzer
            repositoryName={selectedRepository.name}
            onAnalysisComplete={handleAnalysisComplete}
          />
        )}

        {currentStep === 'plan' && selectedRepository && analysisData && (
          <TimelinePlanner
            repositoryName={selectedRepository.name}
            totalCommits={analysisData.totalCommits}
            onConfigurationSave={handlePlanningComplete}
          />
        )}

        {currentStep === 'preview' && (
          <PostPreview
            posts={[]}
            onPostApprove={(postId) => {
              console.log('Post aprovado:', postId);
            }}
            onPostReject={(postId) => {
              console.log('Post rejeitado:', postId);
            }}
            onPostEdit={(postId, newContent) => {
              console.log('Post editado:', postId, newContent);
            }}
            onAllApproved={() => {
              setCurrentStep('activate');
            }}
          />
        )}

        {currentStep === 'activate' && (
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-green-400" />
                Timeline Ativada!
              </CardTitle>
              <CardDescription className="text-slate-300">
                Sua timeline retroativa está pronta e será publicada conforme programado
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Play className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Timeline Ativada com Sucesso!</h3>
                <p className="text-slate-400 mb-6">
                  Seus posts serão publicados automaticamente conforme o cronograma definido
                </p>
                <div className="flex justify-center space-x-3">
                  <Button
                    onClick={() => navigate('/dashboard')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    Ir para Dashboard
                  </Button>
                  <Button
                    onClick={resetTimeline}
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Nova Timeline
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
