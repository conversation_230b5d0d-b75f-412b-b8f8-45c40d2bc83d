import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Sparkles, Code, Zap, ArrowLeft, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
          <Code size={24} />
        </div>
        <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
          <Sparkles size={24} />
        </div>
        <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
          <Zap size={24} />
        </div>
      </div>

      <div className="relative z-10 min-h-screen p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-4 pt-8">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Política de Privacidade
              </h1>
            </div>
            <p className="text-slate-300 text-lg">
              Última atualização: 19 de Janeiro de 2025
            </p>
          </div>

          {/* Back Button */}
          <div className="flex justify-start">
            <Link to="/">
              <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Voltar ao Início
              </Button>
            </Link>
          </div>

          {/* Privacy Content */}
          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">1. Informações que Coletamos</CardTitle>
              <CardDescription className="text-slate-300">
                Tipos de dados que coletamos e como os utilizamos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">1.1 Informações da Conta</h3>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Nome completo e informações de perfil</li>
                  <li>Endereço de email</li>
                  <li>Informações do GitHub (nome de usuário, avatar, repositórios públicos)</li>
                  <li>Tokens de acesso do GitHub (armazenados de forma segura)</li>
                </ul>

                <h3 className="text-lg font-semibold text-white">1.2 Dados de Uso</h3>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Atividades de desenvolvimento (commits, repositórios)</li>
                  <li>Posts gerados e publicados</li>
                  <li>Preferências de configuração</li>
                  <li>Logs de acesso e uso do serviço</li>
                </ul>

                <h3 className="text-lg font-semibold text-white">1.3 Dados Técnicos</h3>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Endereço IP e informações de localização</li>
                  <li>Informações do navegador e dispositivo</li>
                  <li>Cookies e tecnologias similares</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">2. Como Usamos Suas Informações</CardTitle>
              <CardDescription className="text-slate-300">
                Propósitos para os quais utilizamos seus dados pessoais.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Fornecer e manter o serviço Code2Post</li>
                  <li>Processar e analisar suas atividades do GitHub</li>
                  <li>Gerar posts personalizados para LinkedIn</li>
                  <li>Melhorar e otimizar nossos serviços</li>
                  <li>Comunicar atualizações e novidades</li>
                  <li>Prevenir fraudes e garantir a segurança</li>
                  <li>Cumprir obrigações legais</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">3. Compartilhamento de Dados</CardTitle>
              <CardDescription className="text-slate-300">
                Quando e com quem compartilhamos suas informações.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  <strong>Não vendemos, alugamos ou comercializamos</strong> suas informações pessoais 
                  com terceiros para fins de marketing.
                </p>
                
                <p>Podemos compartilhar dados apenas nas seguintes situações:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Com seu consentimento explícito</li>
                  <li>Para cumprir obrigações legais</li>
                  <li>Com prestadores de serviços confiáveis (processamento de pagamentos, hospedagem)</li>
                  <li>Para proteger nossos direitos e segurança</li>
                  <li>Em caso de fusão ou aquisição da empresa</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">4. Segurança dos Dados</CardTitle>
              <CardDescription className="text-slate-300">
                Como protegemos suas informações pessoais.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Implementamos medidas de segurança técnicas e organizacionais para proteger seus dados:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Criptografia de dados em trânsito e em repouso</li>
                  <li>Autenticação segura e controle de acesso</li>
                  <li>Monitoramento contínuo de segurança</li>
                  <li>Backups regulares e recuperação de dados</li>
                  <li>Treinamento da equipe em práticas de segurança</li>
                  <li>Auditorias regulares de segurança</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">5. Seus Direitos</CardTitle>
              <CardDescription className="text-slate-300">
                Direitos que você tem sobre seus dados pessoais.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>Você tem os seguintes direitos:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li><strong>Acesso:</strong> Solicitar informações sobre dados que temos sobre você</li>
                  <li><strong>Correção:</strong> Solicitar correção de dados imprecisos</li>
                  <li><strong>Exclusão:</strong> Solicitar a exclusão de seus dados pessoais</li>
                  <li><strong>Portabilidade:</strong> Receber seus dados em formato estruturado</li>
                  <li><strong>Oposição:</strong> Opor-se ao processamento de seus dados</li>
                  <li><strong>Restrição:</strong> Limitar como processamos seus dados</li>
                  <li><strong>Revogação:</strong> Revogar consentimento a qualquer momento</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">6. Retenção de Dados</CardTitle>
              <CardDescription className="text-slate-300">
                Por quanto tempo mantemos suas informações.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Mantemos seus dados pessoais apenas pelo tempo necessário para:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Fornecer nossos serviços</li>
                  <li>Cumprir obrigações legais</li>
                  <li>Resolver disputas</li>
                  <li>Fazer cumprir nossos acordos</li>
                </ul>
                <p>
                  Quando você exclui sua conta, removemos seus dados pessoais dentro de 30 dias, 
                  exceto quando necessário para fins legais ou de segurança.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">7. Cookies e Tecnologias Similares</CardTitle>
              <CardDescription className="text-slate-300">
                Como usamos cookies e tecnologias de rastreamento.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Utilizamos cookies e tecnologias similares para:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Manter você logado em sua conta</li>
                  <li>Lembrar suas preferências</li>
                  <li>Analisar o uso do serviço</li>
                  <li>Melhorar a experiência do usuário</li>
                  <li>Fornecer funcionalidades personalizadas</li>
                </ul>
                <p>
                  Você pode controlar o uso de cookies através das configurações do seu navegador.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">8. Transferências Internacionais</CardTitle>
              <CardDescription className="text-slate-300">
                Como lidamos com transferências de dados entre países.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Seus dados podem ser transferidos e processados em países diferentes do seu país de residência. 
                  Garantimos que essas transferências sejam feitas de acordo com as leis de proteção de dados aplicáveis.
                </p>
                <p>
                  Para transferências para países que não oferecem proteção adequada, implementamos 
                  salvaguardas apropriadas, como cláusulas contratuais padrão.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">9. Alterações na Política</CardTitle>
              <CardDescription className="text-slate-300">
                Como notificamos sobre mudanças nesta política.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Podemos atualizar esta Política de Privacidade periodicamente. Notificaremos sobre 
                  mudanças significativas através de:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Email para o endereço registrado</li>
                  <li>Notificação no aplicativo</li>
                  <li>Atualização da data de "última atualização"</li>
                </ul>
                <p>
                  Recomendamos revisar esta política regularmente para se manter informado sobre 
                  como protegemos suas informações.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">10. Contato</CardTitle>
              <CardDescription className="text-slate-300">
                Como entrar em contato sobre questões de privacidade.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <p>
                  Se você tiver dúvidas sobre esta Política de Privacidade ou sobre como tratamos seus dados, 
                  entre em contato conosco:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Email: <EMAIL></li>
                  <li>Website: https://code2post.com</li>
                  <li>GitHub: https://github.com/gabrielcamarate/Code2Post</li>
                </ul>
                <p>
                  Responderemos a todas as solicitações dentro de 30 dias.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center space-y-4 pb-8">
            <p className="text-slate-400 text-sm">
              <Link to="/terms-of-service" className="text-blue-400 hover:text-blue-300 transition-colors">
                Termos de Serviço
              </Link>
              {' • '}
              <Link to="/cookie-policy" className="text-blue-400 hover:text-blue-300 transition-colors">
                Política de Cookies
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 
