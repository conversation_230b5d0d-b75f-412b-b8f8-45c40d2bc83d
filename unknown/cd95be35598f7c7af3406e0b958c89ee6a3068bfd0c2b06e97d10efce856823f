# Production Environment Configuration
NODE_ENV=production
PORT=3001

# Database
DATABASE_URL=your_database_url

# JWT (usando as configurações atuais)
JWT_SECRET=681bfc4dd0aa440c0929c30c700f6d58b0edc36ebafbe6ded94aa847394fe0549a75e113f12aecb35bdc84809cf8cd848f47eca8c06a7215b2fd8a520d3de687
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here

# GitHub OAuth - Production
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=3f6134104e99c827da5dd5aabdb21c5fda25303f
GITHUB_CALLBACK_URL=https://www.code2post.com/auth/github/callback

# Gemini AI
GEMINI_API_KEY=AIzaSyDJiOE04xfygIWMFeqzX1-XsyxLoukYN1M

# LinkedIn API (for future use)
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here

# CORS
CORS_ORIGIN=https://www.code2post.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Debug
DEBUG=false
LOG_LEVEL=error
