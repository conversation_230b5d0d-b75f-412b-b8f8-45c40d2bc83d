import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, GitBranch, Sparkles, FileText, Settings } from 'lucide-react';

export default function QuickActions() {
  const actions = [
    {
      icon: Plus,
      label: 'Novo Post',
      description: 'Gerar post com IA',
      gradient: 'from-blue-500 to-purple-500',
      onClick: () => console.log('Novo post')
    },
    {
      icon: GitBranch,
      label: 'Conectar Repo',
      description: 'Adicionar repositório',
      gradient: 'from-purple-500 to-pink-500',
      onClick: () => console.log('Conectar repo')
    },
    {
      icon: Sparkles,
      label: 'IA Assistant',
      description: 'Otimizar conteúdo',
      gradient: 'from-pink-500 to-red-500',
      onClick: () => console.log('IA Assistant')
    },
    {
      icon: FileText,
      label: 'Templates',
      description: 'Gerenciar templates',
      gradient: 'from-green-500 to-cyan-500',
      onClick: () => console.log('Templates')
    }
  ];

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Settings className="w-5 h-5 mr-2 text-blue-400" />
          Ações Rápidas
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Button
                key={index}
                onClick={action.onClick}
                className={`
                  h-auto p-4 flex flex-col items-center space-y-2 
                  bg-gradient-to-r ${action.gradient} hover:scale-105 
                  transition-all duration-300 text-white border-0
                  hover:shadow-lg hover:shadow-blue-500/25
                `}
              >
                <Icon size={20} />
                <div className="text-center">
                  <div className="text-sm font-medium">{action.label}</div>
                  <div className="text-xs opacity-80">{action.description}</div>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
