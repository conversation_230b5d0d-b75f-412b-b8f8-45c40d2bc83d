import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import app from './app.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const PORT = process.env.PORT || 3001;
const HTTPS_PORT = process.env.HTTPS_PORT || 3443;

// Configurações de certificados SSL
const sslOptions = {
  key: fs.readFileSync(path.join(__dirname, '../ssl/private-key.pem')),
  cert: fs.readFileSync(path.join(__dirname, '../ssl/certificate.pem')),
  ca: fs.readFileSync(path.join(__dirname, '../ssl/ca-bundle.pem')),
};

// Criar servidor HTTPS
const httpsServer = https.createServer(sslOptions, app);

// Iniciar servidor HTTPS
httpsServer.listen(HTTPS_PORT, () => {
  console.log(`🔒 Servidor HTTPS rodando na porta ${HTTPS_PORT}`);
  console.log(`🌐 Acesse: https://localhost:${HTTPS_PORT}`);
  console.log(`📊 Health check: https://localhost:${HTTPS_PORT}/health`);
});

// Tratamento de erros
httpsServer.on('error', error => {
  console.error('❌ Erro no servidor HTTPS:', error);
});

export default httpsServer;
