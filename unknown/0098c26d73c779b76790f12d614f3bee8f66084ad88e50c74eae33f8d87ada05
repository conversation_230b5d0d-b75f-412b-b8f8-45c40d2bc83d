import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export function Loading({ size = 'md', text = 'Carregando...', className }: LoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
      <div className={cn(
        'border-2 border-blue-500 border-t-transparent rounded-full animate-spin',
        sizeClasses[size]
      )}></div>
      {text && (
        <p className="text-slate-300 text-sm">{text}</p>
      )}
    </div>
  );
}

export function LoadingScreen({ text = 'Carregando...' }: { text?: string }) {
  return (
    <div className="min-h-screen bg-slate-900 text-white flex items-center justify-center">
      <Loading size="lg" text={text} />
    </div>
  );
}
