import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>rk<PERSON>, Code, Zap, ArrowLeft, <PERSON><PERSON> } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { PolicyFooter } from '@/components/layout/PolicyFooter';

export default function CookiePolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-32 left-32 text-blue-400/20 animate-bounce">
          <Code size={24} />
        </div>
        <div className="absolute top-48 right-48 text-purple-400/20 animate-bounce delay-300">
          <Sparkles size={24} />
        </div>
        <div className="absolute bottom-32 left-48 text-cyan-400/20 animate-bounce delay-700">
          <Zap size={24} />
        </div>
      </div>

      <div className="relative z-10 min-h-screen p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-4 pt-8">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Cookie className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Política de Cookies
              </h1>
            </div>
            <p className="text-slate-300 text-lg">
              Última atualização: 25 de julho de 2025
            </p>
          </div>

          {/* Back Button */}
          <div className="flex justify-start">
            <Link to="/">
              <Button variant="ghost" className="text-slate-400 hover:text-slate-300 group">
                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Voltar ao Início
              </Button>
            </Link>
          </div>

          {/* Cookie Content */}
          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">O que são Cookies?</CardTitle>
              <CardDescription className="text-slate-300">
                Cookies são pequenos arquivos de texto que são armazenados no seu dispositivo quando você visita nosso site.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <p>
                Eles nos ajudam a fornecer uma melhor experiência de usuário e a entender como nosso site é usado.
              </p>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Como Usamos Cookies</CardTitle>
              <CardDescription className="text-slate-300">
                Utilizamos cookies para melhorar sua experiência e funcionalidade do site.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Manter você logado em sua conta</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Lembrar suas preferências e configurações</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Analisar como nosso site é usado (analytics)</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Melhorar a performance e funcionalidade do site</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-pink-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Personalizar sua experiência</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Tipos de Cookies que Usamos</CardTitle>
              <CardDescription className="text-slate-300">
                Utilizamos diferentes tipos de cookies para diferentes finalidades.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-6">
                <div className="border-l-4 border-blue-400 pl-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Cookies Essenciais</h3>
                  <p className="text-slate-300">
                    Necessários para o funcionamento básico do site. Incluem cookies de autenticação e segurança.
                  </p>
                </div>

                <div className="border-l-4 border-purple-400 pl-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Cookies de Performance</h3>
                  <p className="text-slate-300">
                    Coletam informações sobre como você usa nosso site para nos ajudar a melhorá-lo.
                  </p>
                </div>

                <div className="border-l-4 border-cyan-400 pl-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Cookies de Funcionalidade</h3>
                  <p className="text-slate-300">
                    Permitem que o site lembre suas escolhas e forneça recursos aprimorados.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Cookies de Terceiros</CardTitle>
              <CardDescription className="text-slate-300">
                Também utilizamos alguns cookies de terceiros confiáveis para melhorar nossos serviços.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <strong className="text-white">Google Analytics:</strong> Para entender como os usuários interagem com nosso site
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <strong className="text-white">GitHub OAuth:</strong> Para autenticação segura com sua conta GitHub
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Gerenciar Cookies</CardTitle>
              <CardDescription className="text-slate-300">
                Você pode controlar e gerenciar cookies de várias maneiras.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong className="text-white">Configurações do navegador:</strong> A maioria dos navegadores permite bloquear ou excluir cookies</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong className="text-white">Opt-out de analytics:</strong> Você pode desativar o Google Analytics usando suas ferramentas de opt-out</span>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong className="text-white">Configurações da conta:</strong> Algumas preferências podem ser gerenciadas em sua conta</span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-200">
                  <strong>Nota:</strong> Desabilitar cookies essenciais pode afetar a funcionalidade do site.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Atualizações desta Política</CardTitle>
              <CardDescription className="text-slate-300">
                Mantemos nossa política de cookies atualizada com as melhores práticas.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <p>
                Podemos atualizar esta política de cookies ocasionalmente. Recomendamos que você revise
                esta página periodicamente para se manter informado sobre como usamos cookies.
              </p>
            </CardContent>
          </Card>

          <Card className="w-full bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-white">Contato</CardTitle>
              <CardDescription className="text-slate-300">
                Estamos aqui para esclarecer qualquer dúvida sobre nossa política de cookies.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-300">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">@</span>
                </div>
                <div>
                  <p className="text-slate-300">
                    Se você tiver dúvidas sobre nossa política de cookies, entre em contato conosco em:
                  </p>
                  <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 font-medium">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>

          <PolicyFooter currentPage="cookies" />
        </div>
      </div>
    </div>
  );
}
