import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
  delay?: number;
}

export function TestimonialCard({ 
  name, 
  role, 
  company, 
  content, 
  rating, 
  avatar, 
  delay = 0 
}: TestimonialCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card 
      className={`relative overflow-hidden transition-all duration-500 transform ${
        isHovered 
          ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20' 
          : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
      } backdrop-blur-xl`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ animationDelay: `${delay}ms` }}
    >
      {/* Efeito de brilho */}
      <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 transition-opacity duration-500 ${
        isHovered ? 'opacity-100' : 'opacity-0'
      }`} />
      
      {/* Quote icon */}
      <div className={`absolute top-4 right-4 transition-all duration-300 ${
        isHovered ? 'scale-110 text-blue-400' : 'text-slate-600'
      }`}>
        <Quote className="w-8 h-8" />
      </div>
      
      <CardContent className="p-6 relative z-10">
        {/* Rating */}
        <div className="flex items-center mb-4">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`w-5 h-5 transition-all duration-300 ${
                i < rating 
                  ? isHovered 
                    ? 'text-yellow-400 scale-110' 
                    : 'text-yellow-500'
                  : 'text-slate-600'
              }`}
              fill={i < rating ? 'currentColor' : 'none'}
            />
          ))}
        </div>
        
        {/* Content */}
        <p className={`text-slate-300 mb-6 leading-relaxed transition-colors duration-300 ${
          isHovered ? 'text-slate-200' : ''
        }`}>
          "{content}"
        </p>
        
        {/* Author */}
        <div className="flex items-center space-x-4">
          <div className={`relative transition-transform duration-300 ${
            isHovered ? 'scale-110' : ''
          }`}>
            <div className={`w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold transition-all duration-300 ${
              isHovered ? 'shadow-lg shadow-blue-500/50' : ''
            }`}>
              {avatar}
            </div>
            {isHovered && (
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 animate-ping opacity-30" />
            )}
          </div>
          <div>
            <div className={`font-semibold transition-colors duration-300 ${
              isHovered ? 'text-blue-300' : 'text-white'
            }`}>
              {name}
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isHovered ? 'text-slate-300' : 'text-slate-400'
            }`}>
              {role} • {company}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
