import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Calendar, 
  Clock, 
  Settings, 
  Play, 
  Pause,
  Edit3,
  Eye,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  Zap,
  Target,
  GitCommit,
  MessageSquare,
  TrendingUp,
  Users
} from 'lucide-react';

interface PlannedPost {
  id: string;
  week: number;
  day: string;
  date: string;
  title: string;
  content: string;
  commits: string[];
  type: 'milestone' | 'feature' | 'bugfix' | 'update';
  estimatedEngagement: number;
  hashtags: string[];
  status: 'scheduled' | 'draft' | 'published';
}

interface TimelineConfig {
  frequency: 'daily' | 'weekly' | 'custom';
  duration: number; // weeks
  postsPerWeek: number;
  style: 'technical' | 'casual' | 'professional';
  startDate: string;
}

interface TimelinePlannerProps {
  repositoryName: string;
  totalCommits: number;
  onConfigurationSave: (config: TimelineConfig, posts: PlannedPost[]) => void;
}

export default function TimelinePlanner({ 
  repositoryName, 
  totalCommits, 
  onConfigurationSave 
}: TimelinePlannerProps) {
  const [config, setConfig] = useState<TimelineConfig>({
    frequency: 'weekly',
    duration: 12,
    postsPerWeek: 2,
    style: 'professional',
    startDate: new Date().toISOString().split('T')[0]
  });

  const [plannedPosts, setPlannedPosts] = useState<PlannedPost[]>([]);
  const [currentWeek, setCurrentWeek] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);

  // Dados fake para demonstração
  const mockPosts: PlannedPost[] = [
    {
      id: '1',
      week: 1,
      day: 'Segunda',
      date: '2025-01-27',
      title: '🚀 Iniciando o Code2Post: A ideia por trás da automação',
      content: 'Hoje começamos uma jornada incrível! A ideia do Code2Post surgiu da necessidade de transformar nosso trabalho de desenvolvimento em conteúdo engajante para o LinkedIn...',
      commits: ['Initial commit', 'Setup project structure'],
      type: 'milestone',
      estimatedEngagement: 85,
      hashtags: ['#coding', '#startup', '#linkedin', '#automation'],
      status: 'scheduled'
    },
    {
      id: '2',
      week: 1,
      day: 'Quinta',
      date: '2025-01-30',
      title: '⚙️ Configurando a base: Backend com Node.js e Express',
      content: 'Estruturando o backend da aplicação com Node.js e Express. Escolhemos essa stack pela flexibilidade e performance...',
      commits: ['Setup Express server', 'Configure middleware', 'Add JWT authentication'],
      type: 'feature',
      estimatedEngagement: 72,
      hashtags: ['#nodejs', '#express', '#backend', '#javascript'],
      status: 'scheduled'
    },
    {
      id: '3',
      week: 2,
      day: 'Segunda',
      date: '2025-02-03',
      title: '🔐 Implementando autenticação segura com GitHub OAuth',
      content: 'Segurança em primeiro lugar! Implementamos autenticação via GitHub OAuth para garantir que apenas usuários autorizados acessem a plataforma...',
      commits: ['Implement GitHub OAuth', 'Add user session management', 'Security middleware'],
      type: 'feature',
      estimatedEngagement: 78,
      hashtags: ['#security', '#oauth', '#github', '#authentication'],
      status: 'scheduled'
    },
    {
      id: '4',
      week: 2,
      day: 'Quinta',
      date: '2025-02-06',
      title: '🤖 Integrando IA: Gemini API para geração de conteúdo',
      content: 'A mágica acontece aqui! Integramos a Gemini API para transformar commits técnicos em posts envolventes e humanizados...',
      commits: ['Integrate Gemini API', 'Create content generation service', 'Add AI prompt templates'],
      type: 'feature',
      estimatedEngagement: 92,
      hashtags: ['#ai', '#gemini', '#contentgeneration', '#innovation'],
      status: 'scheduled'
    }
  ];

  useEffect(() => {
    setPlannedPosts(mockPosts);
  }, []);

  const generateTimeline = async () => {
    setIsGenerating(true);
    
    // Simular geração de timeline
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setPlannedPosts(mockPosts);
    setIsGenerating(false);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone': return 'bg-purple-500/10 text-purple-400 border-purple-500/30';
      case 'feature': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'bugfix': return 'bg-red-500/10 text-red-400 border-red-500/30';
      case 'update': return 'bg-green-500/10 text-green-400 border-green-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'milestone': return Target;
      case 'feature': return Zap;
      case 'bugfix': return Settings;
      case 'update': return TrendingUp;
      default: return GitCommit;
    }
  };

  const currentWeekPosts = plannedPosts.filter(post => post.week === currentWeek);
  const totalWeeks = Math.max(...plannedPosts.map(post => post.week), 1);

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Calendar className="w-5 h-5 mr-2 text-blue-400" />
          Planejador de Timeline
        </CardTitle>
        <CardDescription className="text-slate-300">
          Configure e visualize sua timeline de posts para <span className="text-blue-400 font-medium">{repositoryName}</span>
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* Configurações */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 p-4 bg-slate-700/20 rounded-lg border border-slate-600/30">
          <div>
            <Label className="text-slate-300 text-sm">Frequência</Label>
            <select 
              value={config.frequency}
              onChange={(e) => setConfig({...config, frequency: e.target.value as any})}
              className="w-full mt-1 bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white text-sm"
            >
              <option value="daily">Diária</option>
              <option value="weekly">Semanal</option>
              <option value="custom">Personalizada</option>
            </select>
          </div>

          <div>
            <Label className="text-slate-300 text-sm">Duração</Label>
            <Input
              type="number"
              value={config.duration}
              onChange={(e) => setConfig({...config, duration: parseInt(e.target.value)})}
              className="mt-1 bg-slate-700 border-slate-600 text-white"
              min="1"
              max="52"
            />
            <span className="text-xs text-slate-400">semanas</span>
          </div>

          <div>
            <Label className="text-slate-300 text-sm">Posts/Semana</Label>
            <Input
              type="number"
              value={config.postsPerWeek}
              onChange={(e) => setConfig({...config, postsPerWeek: parseInt(e.target.value)})}
              className="mt-1 bg-slate-700 border-slate-600 text-white"
              min="1"
              max="7"
            />
          </div>

          <div>
            <Label className="text-slate-300 text-sm">Estilo</Label>
            <select 
              value={config.style}
              onChange={(e) => setConfig({...config, style: e.target.value as any})}
              className="w-full mt-1 bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white text-sm"
            >
              <option value="technical">Técnico</option>
              <option value="casual">Casual</option>
              <option value="professional">Profissional</option>
            </select>
          </div>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-slate-700/30 rounded-lg p-3 border border-slate-600/30">
            <div className="flex items-center justify-between">
              <GitCommit className="w-4 h-4 text-blue-400" />
              <span className="text-lg font-bold text-white">{totalCommits}</span>
            </div>
            <p className="text-xs text-slate-300">Commits Totais</p>
          </div>

          <div className="bg-slate-700/30 rounded-lg p-3 border border-slate-600/30">
            <div className="flex items-center justify-between">
              <MessageSquare className="w-4 h-4 text-green-400" />
              <span className="text-lg font-bold text-white">{plannedPosts.length}</span>
            </div>
            <p className="text-xs text-slate-300">Posts Planejados</p>
          </div>

          <div className="bg-slate-700/30 rounded-lg p-3 border border-slate-600/30">
            <div className="flex items-center justify-between">
              <Calendar className="w-4 h-4 text-purple-400" />
              <span className="text-lg font-bold text-white">{config.duration}</span>
            </div>
            <p className="text-xs text-slate-300">Semanas de Conteúdo</p>
          </div>

          <div className="bg-slate-700/30 rounded-lg p-3 border border-slate-600/30">
            <div className="flex items-center justify-between">
              <Users className="w-4 h-4 text-orange-400" />
              <span className="text-lg font-bold text-white">
                {Math.round(plannedPosts.reduce((acc, post) => acc + post.estimatedEngagement, 0) / plannedPosts.length)}%
              </span>
            </div>
            <p className="text-xs text-slate-300">Engajamento Médio</p>
          </div>
        </div>

        {/* Navegação de Semanas */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentWeek(Math.max(1, currentWeek - 1))}
              disabled={currentWeek === 1}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <div className="px-4 py-2 bg-slate-700/50 rounded-lg border border-slate-600/30">
              <span className="text-white font-medium">Semana {currentWeek}</span>
              <span className="text-slate-400 text-sm ml-2">de {totalWeeks}</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentWeek(Math.min(totalWeeks, currentWeek + 1))}
              disabled={currentWeek === totalWeeks}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <Button
            onClick={generateTimeline}
            disabled={isGenerating}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isGenerating ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                Gerar Timeline
              </>
            )}
          </Button>
        </div>

        {/* Posts da Semana Atual */}
        <div className="space-y-4">
          {currentWeekPosts.length > 0 ? (
            currentWeekPosts.map((post) => {
              const TypeIcon = getTypeIcon(post.type);
              return (
                <div
                  key={post.id}
                  className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30 hover:bg-slate-700/50 transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <TypeIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-white font-medium text-sm">{post.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-slate-400">{post.day}, {post.date}</span>
                          <Badge className={getTypeColor(post.type)}>
                            {post.type}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-400">{post.estimatedEngagement}%</div>
                        <div className="text-xs text-slate-400">engajamento</div>
                      </div>
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <p className="text-slate-300 text-sm mb-3 line-clamp-2">
                    {post.content}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-slate-400">
                        {post.commits.length} commits
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {post.hashtags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="text-xs text-blue-400 bg-blue-500/10 px-2 py-1 rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                        <Edit3 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">Nenhum post planejado para esta semana</p>
            </div>
          )}
        </div>

        {/* Ações */}
        <div className="flex items-center justify-between mt-6 pt-6 border-t border-slate-600/30">
          <div className="text-sm text-slate-400">
            Timeline configurada para {config.duration} semanas • {plannedPosts.length} posts planejados
          </div>
          
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
              <Pause className="w-4 h-4 mr-2" />
              Salvar Rascunho
            </Button>
            <Button 
              onClick={() => onConfigurationSave(config, plannedPosts)}
              className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700"
            >
              <Play className="w-4 h-4 mr-2" />
              Ativar Timeline
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
