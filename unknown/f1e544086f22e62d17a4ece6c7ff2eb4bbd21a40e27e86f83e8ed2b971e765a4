import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  FileText,
  Search,
  Filter,
  Plus,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Edit3,
  Trash2,
  ExternalLink,
  ThumbsUp,
  MessageCircle,
  Share,
  TrendingUp,
  Eye,
  MoreHorizontal,
  RefreshCw,
  Send,
  X,
  Heart,
  Repeat2,
  BarChart3,
  Target,
  Users
} from 'lucide-react';

interface Post {
  id: string;
  title: string;
  content: string;
  repository: string;
  status: 'published' | 'scheduled' | 'draft' | 'failed';
  publishedAt?: string;
  scheduledFor?: string;
  createdAt: string;
  updatedAt: string;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    views: number;
    reach: number;
  };
  hashtags: string[];
  linkedinUrl?: string;
  type: 'milestone' | 'feature' | 'bugfix' | 'update';
  commits: string[];
}

type FilterType = 'all' | 'published' | 'scheduled' | 'draft' | 'failed';
type SortType = 'recent' | 'engagement' | 'reach' | 'alphabetical';

export default function Posts() {
  const { user, loading } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('recent');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    );
  }

  // Dados fake para demonstração
  const posts: Post[] = [
    {
      id: '1',
      title: '🚀 Code2Post - Parte 3/12: Implementando Timeline Retroativa',
      content: `Hoje foi um dia incrível de desenvolvimento! 🚀

Implementamos a funcionalidade mais revolucionária do Code2Post: a Timeline Retroativa!

💡 O problema que resolvemos:
90% dos desenvolvedores têm projetos prontos com muitos commits, mas não sabem como transformar isso em conteúdo engajante para o LinkedIn.

✨ Nossa solução:
• Análise inteligente do histórico de commits
• Distribuição automática ao longo do tempo
• Narrativa autêntica de desenvolvimento
• Posts que contam a história real do projeto

🎯 Resultados de hoje:
• TimelineAnalyzer implementado
• TimelinePlanner funcionando
• PostPreview com simulação do LinkedIn
• Fluxo completo end-to-end

Próximo passo: finalizar as páginas internas! 💪

#CodeToPost #Timeline #LinkedIn #GitHub #IA`,
      repository: 'Code2Post',
      status: 'published',
      publishedAt: '2025-01-26T14:30:00Z',
      createdAt: '2025-01-26T14:25:00Z',
      updatedAt: '2025-01-26T14:30:00Z',
      metrics: {
        likes: 47,
        comments: 12,
        shares: 8,
        views: 1240,
        reach: 3200
      },
      hashtags: ['#CodeToPost', '#Timeline', '#LinkedIn', '#GitHub', '#IA'],
      linkedinUrl: 'https://linkedin.com/posts/gabriel-camarate_codetopost-timeline-linkedin-activity-123456789',
      type: 'milestone',
      commits: ['feat: implement TimelineAnalyzer', 'feat: add TimelinePlanner', 'feat: create PostPreview']
    },
    {
      id: '2',
      title: '⚙️ Code2Post - Parte 2/12: Configurando Backend com Node.js',
      content: `Estruturando a base sólida do Code2Post! ⚙️

Hoje focamos na arquitetura backend que vai sustentar toda nossa aplicação.

🏗️ O que implementamos:
• Servidor Express configurado
• Sistema de autenticação JWT
• Middleware de segurança
• Integração com GitHub API
• Rate limiting para proteção

🔧 Decisões técnicas importantes:
• Node.js + Express pela flexibilidade
• JWT para autenticação stateless
• Estrutura modular para escalabilidade
• Logs estruturados para debugging

A base está pronta para receber as funcionalidades avançadas! 

Próximo: implementar a IA com Gemini API 🤖

#NodeJS #Express #Backend #API #Segurança`,
      repository: 'Code2Post',
      status: 'scheduled',
      scheduledFor: '2025-01-27T09:00:00Z',
      createdAt: '2025-01-26T16:45:00Z',
      updatedAt: '2025-01-26T16:50:00Z',
      metrics: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0,
        reach: 0
      },
      hashtags: ['#NodeJS', '#Express', '#Backend', '#API', '#Segurança'],
      type: 'feature',
      commits: ['feat: setup Express server', 'feat: add JWT auth', 'feat: implement middleware']
    },
    {
      id: '3',
      title: '🎨 Portfolio - Redesign Completo com React e Tailwind',
      content: `Meu portfólio ganhou uma cara nova! 🎨

Depois de meses usando a versão antiga, decidi fazer um redesign completo focando em:

✨ Melhorias implementadas:
• Design moderno com Tailwind CSS
• Animações suaves e micro-interações
• Seção de projetos mais visual
• Performance otimizada
• Mobile-first approach

🚀 Tecnologias utilizadas:
• React 18 com hooks modernos
• Tailwind CSS para estilização
• Framer Motion para animações
• Vercel para deploy automático

O resultado ficou incrível! Link na bio para conferir 👆

#Portfolio #React #TailwindCSS #WebDev #Frontend`,
      repository: 'Portfolio',
      status: 'published',
      publishedAt: '2025-01-25T11:15:00Z',
      createdAt: '2025-01-25T11:10:00Z',
      updatedAt: '2025-01-25T11:15:00Z',
      metrics: {
        likes: 23,
        comments: 5,
        shares: 3,
        views: 890,
        reach: 1800
      },
      hashtags: ['#Portfolio', '#React', '#TailwindCSS', '#WebDev', '#Frontend'],
      linkedinUrl: 'https://linkedin.com/posts/gabriel-camarate_portfolio-react-tailwindcss-activity-987654321',
      type: 'update',
      commits: ['feat: redesign homepage', 'feat: add animations', 'feat: optimize performance']
    },
    {
      id: '4',
      title: '🔧 TaskManager - Sistema de Gerenciamento em Desenvolvimento',
      content: `Trabalhando em um novo projeto: TaskManager! 🔧

Um sistema completo de gerenciamento de tarefas com funcionalidades avançadas.

🎯 Funcionalidades planejadas:
• Dashboard com métricas em tempo real
• Colaboração em equipe
• Notificações inteligentes
• Integração com calendário
• Relatórios de produtividade

💻 Stack tecnológico:
• Frontend: React + TypeScript
• Backend: Node.js + Express
• Banco: MongoDB
• Real-time: Socket.io

Ainda em desenvolvimento, mas já com ótimo progresso! 

#TaskManager #Produtividade #React #NodeJS #MongoDB`,
      repository: 'TaskManager',
      status: 'draft',
      createdAt: '2025-01-24T15:20:00Z',
      updatedAt: '2025-01-26T10:30:00Z',
      metrics: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0,
        reach: 0
      },
      hashtags: ['#TaskManager', '#Produtividade', '#React', '#NodeJS', '#MongoDB'],
      type: 'feature',
      commits: ['feat: create task model', 'feat: implement CRUD operations', 'feat: add user authentication']
    },
    {
      id: '5',
      title: '❌ E-commerce API - Falha na Publicação Automática',
      content: `Desenvolvendo uma API REST robusta para e-commerce! 🛒

Funcionalidades implementadas hoje:
• Sistema de autenticação completo
• CRUD de produtos com validações
• Carrinho de compras persistente
• Integração com gateway de pagamento
• Sistema de pedidos

Tecnologias:
• Node.js + Express
• MongoDB com Mongoose
• JWT para autenticação
• Stripe para pagamentos
• Redis para cache

A API está ficando muito robusta! 💪

#EcommerceAPI #NodeJS #MongoDB #API #Backend`,
      repository: 'E-commerce-API',
      status: 'failed',
      createdAt: '2025-01-23T13:45:00Z',
      updatedAt: '2025-01-23T14:00:00Z',
      metrics: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0,
        reach: 0
      },
      hashtags: ['#EcommerceAPI', '#NodeJS', '#MongoDB', '#API', '#Backend'],
      type: 'feature',
      commits: ['feat: add payment integration', 'feat: implement order system', 'fix: authentication middleware']
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'scheduled': return <Clock className="w-4 h-4 text-blue-400" />;
      case 'draft': return <Edit3 className="w-4 h-4 text-yellow-400" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-400" />;
      default: return <AlertCircle className="w-4 h-4 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500/10 text-green-400 border-green-500/30';
      case 'scheduled': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'draft': return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/30';
      case 'failed': return 'bg-red-500/10 text-red-400 border-red-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return 'Publicado';
      case 'scheduled': return 'Agendado';
      case 'draft': return 'Rascunho';
      case 'failed': return 'Falhou';
      default: return 'Desconhecido';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone': return 'bg-purple-500/10 text-purple-400 border-purple-500/30';
      case 'feature': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'bugfix': return 'bg-red-500/10 text-red-400 border-red-500/30';
      case 'update': return 'bg-green-500/10 text-green-400 border-green-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  const filteredPosts = posts
    .filter(post => {
      const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.repository.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filterType === 'all' || post.status === filterType;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'recent': return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'engagement': return (b.metrics.likes + b.metrics.comments) - (a.metrics.likes + a.metrics.comments);
        case 'reach': return b.metrics.reach - a.metrics.reach;
        case 'alphabetical': return a.title.localeCompare(b.title);
        default: return 0;
      }
    });

  const statusCounts = {
    all: posts.length,
    published: posts.filter(p => p.status === 'published').length,
    scheduled: posts.filter(p => p.status === 'scheduled').length,
    draft: posts.filter(p => p.status === 'draft').length,
    failed: posts.filter(p => p.status === 'failed').length,
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsRefreshing(false);
  };

  const handlePostClick = (post: Post) => {
    setSelectedPost(post);
    setShowPreview(true);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setSelectedPost(null);
  };

  const handleEdit = (postId: string) => {
    console.log('Editar post:', postId);
  };

  const handleDelete = (postId: string) => {
    console.log('Deletar post:', postId);
  };

  const handleRepublish = (postId: string) => {
    console.log('Republicar post:', postId);
  };

  const handleSchedule = (postId: string) => {
    console.log('Agendar post:', postId);
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Posts
            </h1>
            <p className="text-slate-300 mt-2">
              Gerencie seus posts do LinkedIn gerados automaticamente
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Sincronizando...' : 'Sincronizar'}
            </Button>
            
            <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              Novo Post
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {Object.entries(statusCounts).map(([status, count]) => (
            <Card 
              key={status}
              className={`bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl cursor-pointer transition-all duration-200 hover:bg-slate-700/50 ${
                filterType === status ? 'ring-2 ring-blue-500/50' : ''
              }`}
              onClick={() => setFilterType(status as FilterType)}
            >
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1">{count}</div>
                <div className="text-sm text-slate-400 capitalize">
                  {status === 'all' ? 'Total' : getStatusText(status)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters and Search */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  placeholder="Buscar posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-slate-400" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortType)}
                  className="bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white text-sm"
                >
                  <option value="recent">Mais Recentes</option>
                  <option value="engagement">Maior Engajamento</option>
                  <option value="reach">Maior Alcance</option>
                  <option value="alphabetical">Alfabética</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Posts List */}
        <div className="space-y-6">
          {filteredPosts.map((post) => (
            <Card
              key={post.id}
              className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl cursor-pointer hover:bg-slate-700/50 transition-all duration-200"
              onClick={() => handlePostClick(post)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <FileText className="w-5 h-5 text-blue-400" />
                      <CardTitle className="text-white text-lg line-clamp-1">{post.title}</CardTitle>
                    </div>
                    <div className="flex items-center space-x-2 mb-3">
                      <Badge className="bg-slate-600/50 text-slate-300 text-xs">
                        {post.repository}
                      </Badge>
                      <Badge className={getTypeColor(post.type)}>
                        {post.type}
                      </Badge>
                      <span className="text-slate-400 text-sm">
                        {new Date(post.createdAt).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(post.status)}
                    <Badge className={getStatusColor(post.status)}>
                      {getStatusText(post.status)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                {/* Content Preview */}
                <div className="mb-4">
                  <p className="text-slate-300 text-sm line-clamp-3 leading-relaxed">
                    {post.content}
                  </p>
                </div>

                {/* Hashtags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {post.hashtags.map((tag, index) => (
                    <span key={index} className="text-xs text-blue-400 bg-blue-500/10 px-2 py-1 rounded">
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Metrics */}
                {post.status === 'published' && (
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4 p-3 bg-slate-700/30 rounded-lg">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <ThumbsUp className="w-4 h-4 text-blue-400" />
                      </div>
                      <div className="text-sm font-medium text-white">{post.metrics.likes}</div>
                      <div className="text-xs text-slate-400">Curtidas</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <MessageCircle className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="text-sm font-medium text-white">{post.metrics.comments}</div>
                      <div className="text-xs text-slate-400">Comentários</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Share className="w-4 h-4 text-purple-400" />
                      </div>
                      <div className="text-sm font-medium text-white">{post.metrics.shares}</div>
                      <div className="text-xs text-slate-400">Compartilhamentos</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Eye className="w-4 h-4 text-orange-400" />
                      </div>
                      <div className="text-sm font-medium text-white">{post.metrics.views}</div>
                      <div className="text-xs text-slate-400">Visualizações</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <TrendingUp className="w-4 h-4 text-cyan-400" />
                      </div>
                      <div className="text-sm font-medium text-white">{post.metrics.reach}</div>
                      <div className="text-xs text-slate-400">Alcance</div>
                    </div>
                  </div>
                )}

                {/* Schedule Info */}
                {post.status === 'scheduled' && post.scheduledFor && (
                  <div className="flex items-center space-x-2 mb-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/30">
                    <Calendar className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 text-sm">
                      Agendado para: {new Date(post.scheduledFor).toLocaleString('pt-BR')}
                    </span>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-slate-600/30">
                  <div className="flex items-center space-x-2">
                    {post.linkedinUrl && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(post.linkedinUrl, '_blank')}
                        className="text-slate-400 hover:text-white"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Ver no LinkedIn
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(post.id)}
                      className="text-slate-400 hover:text-white"
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>

                    {post.status === 'draft' && (
                      <Button
                        size="sm"
                        onClick={() => handleSchedule(post.id)}
                        className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      >
                        <Send className="w-4 h-4 mr-2" />
                        Publicar
                      </Button>
                    )}

                    {post.status === 'failed' && (
                      <Button
                        size="sm"
                        onClick={() => handleRepublish(post.id)}
                        className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700"
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Tentar Novamente
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(post.id)}
                      className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredPosts.length === 0 && (
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardContent className="text-center py-12">
              <FileText className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                Nenhum post encontrado
              </h3>
              <p className="text-slate-400 mb-6">
                {searchTerm || filterType !== 'all' 
                  ? 'Tente ajustar os filtros de busca'
                  : 'Crie seu primeiro post automatizado'
                }
              </p>
              {!searchTerm && filterType === 'all' && (
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Criar Post
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Preview Modal */}
      {showPreview && selectedPost && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900 rounded-2xl border border-slate-700/50 shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex h-full">
              {/* LinkedIn Preview */}
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white">Preview LinkedIn</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClosePreview}
                    className="text-slate-400 hover:text-white"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </div>

                {/* LinkedIn Post Card */}
                <div className="bg-white rounded-lg shadow-lg max-w-lg mx-auto">
                  {/* Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src="/api/placeholder/48/48" />
                        <AvatarFallback className="bg-blue-500 text-white">GC</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-semibold text-gray-900">Gabriel Camarate</div>
                        <div className="text-sm text-gray-500">Desenvolvedor Full Stack • 1º</div>
                        <div className="text-xs text-gray-400 flex items-center">
                          {new Date(selectedPost.publishedAt).toLocaleDateString('pt-BR')} • 🌍
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="text-gray-400">
                        <MoreHorizontal className="w-5 h-5" />
                      </Button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <div className="text-gray-900 whitespace-pre-wrap text-sm leading-relaxed">
                      {selectedPost.content}
                    </div>

                    {/* Hashtags */}
                    <div className="mt-3 flex flex-wrap gap-1">
                      {selectedPost.hashtags.map((tag, index) => (
                        <span key={index} className="text-blue-600 text-sm hover:underline cursor-pointer">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Engagement */}
                  <div className="px-4 py-2 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <div className="flex -space-x-1">
                          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                            <ThumbsUp className="w-3 h-3 text-white" />
                          </div>
                          <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                            <Heart className="w-3 h-3 text-white" />
                          </div>
                        </div>
                        <span>{selectedPost.metrics.likes} curtidas</span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span>{selectedPost.metrics.comments} comentários</span>
                        <span>{selectedPost.metrics.shares} compartilhamentos</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="px-4 py-3 border-t border-gray-200">
                    <div className="flex items-center justify-around">
                      <Button variant="ghost" size="sm" className="flex-1 text-gray-600 hover:bg-gray-100">
                        <ThumbsUp className="w-4 h-4 mr-2" />
                        Curtir
                      </Button>
                      <Button variant="ghost" size="sm" className="flex-1 text-gray-600 hover:bg-gray-100">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Comentar
                      </Button>
                      <Button variant="ghost" size="sm" className="flex-1 text-gray-600 hover:bg-gray-100">
                        <Repeat2 className="w-4 h-4 mr-2" />
                        Compartilhar
                      </Button>
                      <Button variant="ghost" size="sm" className="flex-1 text-gray-600 hover:bg-gray-100">
                        <Send className="w-4 h-4 mr-2" />
                        Enviar
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Metrics and Actions Sidebar */}
              <div className="w-80 bg-slate-800/30 border-l border-slate-700/50 p-6 overflow-y-auto">
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
                  Métricas Previstas
                </h4>

                {/* Metrics Grid */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-blue-400">{selectedPost.metrics.likes}</div>
                    <div className="text-xs text-slate-400">Curtidas</div>
                  </div>
                  <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-green-400">{selectedPost.metrics.comments}</div>
                    <div className="text-xs text-slate-400">Comentários</div>
                  </div>
                  <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-yellow-400">{selectedPost.metrics.shares}</div>
                    <div className="text-xs text-slate-400">Compartilhamentos</div>
                  </div>
                  <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-purple-400">{selectedPost.metrics.reach}</div>
                    <div className="text-xs text-slate-400">Alcance</div>
                  </div>
                </div>

                {/* Engagement Rate */}
                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-4 mb-6 border border-green-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-slate-300 text-sm">Taxa de Engajamento</span>
                    <Target className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="text-2xl font-bold text-green-400">8.5%</div>
                  <div className="text-xs text-green-300">+15% vs média</div>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <h5 className="text-white font-medium">Ações</h5>

                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                    <Edit3 className="w-4 h-4 mr-2" />
                    Editar Conteúdo
                  </Button>

                  {selectedPost.status === 'published' ? (
                    <Button variant="outline" className="w-full border-green-600 text-green-400 hover:bg-green-600/10">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Publicado
                    </Button>
                  ) : selectedPost.status === 'scheduled' ? (
                    <Button className="w-full bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Aprovar
                    </Button>
                  ) : (
                    <Button className="w-full bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700">
                      <Send className="w-4 h-4 mr-2" />
                      Publicar Agora
                    </Button>
                  )}

                  <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                    <Calendar className="w-4 h-4 mr-2" />
                    Reagendar
                  </Button>

                  <Button variant="outline" className="w-full border-red-600 text-red-400 hover:bg-red-600/10">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Excluir
                  </Button>
                </div>

                {/* Post Info */}
                <div className="mt-6 pt-6 border-t border-slate-700/50">
                  <h5 className="text-white font-medium mb-3">Informações</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">Agendado para:</span>
                      <span className="text-slate-300">
                        {new Date(selectedPost.publishedAt).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Repositório:</span>
                      <span className="text-slate-300">{selectedPost.repository}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Tipo:</span>
                      <Badge className={
                        selectedPost.type === 'milestone' ? 'bg-purple-500/10 text-purple-400 border-purple-500/30' :
                        selectedPost.type === 'feature' ? 'bg-green-500/10 text-green-400 border-green-500/30' :
                        selectedPost.type === 'bugfix' ? 'bg-red-500/10 text-red-400 border-red-500/30' :
                        'bg-blue-500/10 text-blue-400 border-blue-500/30'
                      }>
                        {selectedPost.type === 'milestone' ? 'Marco' :
                         selectedPost.type === 'feature' ? 'Feature' :
                         selectedPost.type === 'bugfix' ? 'Correção' : 'Atualização'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
