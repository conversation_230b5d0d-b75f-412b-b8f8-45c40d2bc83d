import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { api } from '@/services';

export default function BackendTest() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const testBackendConnection = async () => {
    setStatus('loading');
    setMessage('Testando conexão...');

    try {
      // Testar o endpoint de health check do backend
      const response = await api.get('/health');
      
      setStatus('success');
      setMessage(`✅ Backend funcionando! Status: ${response.data.status}`);
    } catch (error) {
      setStatus('error');
      setMessage('❌ Erro ao conectar com o backend. Verifique se o servidor está rodando.');
      console.error('Erro de conexão:', error);
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'loading': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'success': return 'Conectado';
      case 'error': return 'Erro';
      case 'loading': return 'Testando...';
      default: return 'Não testado';
    }
  };

  return (
    <Card className="w-full max-w-md bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-blue-400">Teste de Conexão</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-slate-300">Status do Backend:</span>
          <Badge className={getStatusColor()}>
            {getStatusText()}
          </Badge>
        </div>

        <Button 
          onClick={testBackendConnection}
          disabled={status === 'loading'}
          className="w-full"
        >
          {status === 'loading' ? 'Testando...' : 'Testar Conexão'}
        </Button>

        {message && (
          <div className="p-3 bg-slate-700 rounded-md">
            <p className="text-sm text-slate-200">{message}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 