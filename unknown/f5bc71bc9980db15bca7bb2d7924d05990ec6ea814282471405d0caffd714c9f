import jwt from 'jsonwebtoken';

// Simular banco de dados de sessões do usuário
const userSessions = new Map();

/**
 * Middleware para detectar logins suspeitos
 */
export const detectSuspiciousLogin = (req, res, next) => {
  try {
    const { email } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];
    const currentTime = new Date();

    console.log(`🔍 Analisando login para: ${email}`);
    console.log(`📍 IP: ${clientIP}`);
    console.log(`📱 User-Agent: ${userAgent}`);

    // Extrair informações do User-Agent
    const deviceInfo = parseUserAgent(userAgent);
    console.log(`💻 Dispositivo detectado:`, deviceInfo);

    // Verificar se é o primeiro login do usuário
    if (!userSessions.has(email)) {
      console.log(`🆕 Primeiro login para ${email}`);
      // Primeiro login - registrar informações
      userSessions.set(email, {
        lastLogin: currentTime,
        lastIP: clientIP,
        lastUserAgent: userAgent,
        lastDevice: deviceInfo,
        loginCount: 1,
        suspiciousLogins: [],
      });
      return next();
    }

    const userSession = userSessions.get(email);
    console.log(`📊 Sessão anterior:`, {
      lastIP: userSession.lastIP,
      lastUserAgent: userSession.lastUserAgent,
      lastDevice: userSession.lastDevice,
    });

    const isSuspicious = checkSuspiciousActivity(userSession, {
      ip: clientIP,
      userAgent,
      deviceInfo,
      currentTime,
    });

    console.log(`🔍 Resultado da análise:`, isSuspicious);

    if (isSuspicious.isSuspicious) {
      // Registrar login suspeito
      const suspiciousLogin = {
        timestamp: currentTime,
        ip: clientIP,
        userAgent,
        deviceInfo,
        reason: isSuspicious.reason,
      };

      userSession.suspiciousLogins.push(suspiciousLogin);
      userSession.lastLogin = currentTime;
      userSession.lastIP = clientIP;
      userSession.lastUserAgent = userAgent;
      userSession.lastDevice = deviceInfo;
      userSession.loginCount++;

      // Adicionar flag para notificação
      req.suspiciousLogin = suspiciousLogin;

      console.log(
        `🚨 Login suspeito detectado para ${email}:`,
        suspiciousLogin
      );
    } else {
      // Login normal - atualizar informações
      userSession.lastLogin = currentTime;
      userSession.lastIP = clientIP;
      userSession.lastUserAgent = userAgent;
      userSession.lastDevice = deviceInfo;
      userSession.loginCount++;
      console.log(`✅ Login normal para ${email}`);
    }

    next();
  } catch (error) {
    console.error('Erro ao detectar login suspeito:', error);
    next(); // Continuar mesmo com erro
  }
};

/**
 * Verificar se a atividade é suspeita
 */
function checkSuspiciousActivity(userSession, currentLogin) {
  const { ip, userAgent, deviceInfo, currentTime } = currentLogin;
  const { lastIP, lastUserAgent, lastDevice, lastLogin } = userSession;

  const suspiciousReasons = [];

  // 1. Verificar mudança de IP (simplificado - em produção usar geolocalização)
  if (lastIP && ip !== lastIP) {
    suspiciousReasons.push('IP diferente do último login');
  }

  // 2. Verificar mudança de User-Agent
  if (lastUserAgent && userAgent !== lastUserAgent) {
    suspiciousReasons.push('Dispositivo/navegador diferente');
  }

  // 3. Verificar horário incomum (entre 23h e 6h)
  const hour = currentTime.getHours();
  if (hour >= 23 || hour <= 6) {
    suspiciousReasons.push('Login em horário incomum');
  }

  // 4. Verificar mudança significativa de dispositivo
  if (lastDevice && deviceInfo.browser !== lastDevice.browser) {
    suspiciousReasons.push('Navegador diferente');
  }

  if (lastDevice && deviceInfo.os !== lastDevice.os) {
    suspiciousReasons.push('Sistema operacional diferente');
  }

  // 5. Verificar se é muito recente (menos de 1 hora do último login)
  if (lastLogin) {
    const timeDiff = currentTime - lastLogin;
    const oneHour = 60 * 60 * 1000;
    if (timeDiff < oneHour) {
      suspiciousReasons.push('Login muito recente');
    }
  }

  return suspiciousReasons.length > 0
    ? { isSuspicious: true, reason: suspiciousReasons.join(', ') }
    : { isSuspicious: false };
}

/**
 * Extrair informações do User-Agent
 */
function parseUserAgent(userAgent) {
  if (!userAgent) return { browser: 'Unknown', os: 'Unknown' };

  // Detecção simplificada - em produção usar biblioteca como 'ua-parser-js'
  const ua = userAgent.toLowerCase();

  let browser = 'Unknown';
  let os = 'Unknown';

  // Detectar navegador
  if (ua.includes('chrome')) browser = 'Chrome';
  else if (ua.includes('firefox')) browser = 'Firefox';
  else if (ua.includes('safari')) browser = 'Safari';
  else if (ua.includes('edge')) browser = 'Edge';
  else if (ua.includes('opera')) browser = 'Opera';

  // Detectar sistema operacional
  if (ua.includes('windows')) os = 'Windows';
  else if (ua.includes('mac')) os = 'macOS';
  else if (ua.includes('linux')) os = 'Linux';
  else if (ua.includes('android')) os = 'Android';
  else if (ua.includes('ios')) os = 'iOS';

  return { browser, os };
}

/**
 * Obter estatísticas de login do usuário
 */
export const getUserLoginStats = email => {
  return userSessions.get(email) || null;
};

/**
 * Limpar sessões antigas (manutenção)
 */
export const cleanupOldSessions = () => {
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

  for (const [email, session] of userSessions.entries()) {
    if (session.lastLogin < oneMonthAgo) {
      userSessions.delete(email);
    }
  }
};

// Executar limpeza a cada 24 horas
setInterval(cleanupOldSessions, 24 * 60 * 60 * 1000);
