import { Spinner } from "@/components/ui/spinner"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function SpinnerTest() {
  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Spinner Component Test</h1>
        <p className="text-muted-foreground">Testando o componente de loading customizado</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Small Spinner</CardTitle>
            <CardDescription>Tamanho pequeno</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Spinner size="sm" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Medium Spinner</CardTitle>
            <CardDescription>Tam<PERSON>ho médio (padrão)</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Spinner size="md" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Large Spinner</CardTitle>
            <CardDescription>Tamanho grande</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Spinner size="lg" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Extra Large Spinner</CardTitle>
            <CardDescription>Tamanho extra grande</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Spinner size="xl" />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Spinner com Botão</CardTitle>
          <CardDescription>Exemplo de uso em botões de loading</CardDescription>
        </CardHeader>
        <CardContent className="flex gap-4">
          <Button disabled>
            <Spinner size="sm" className="mr-2" />
            Carregando...
          </Button>
          
          <Button variant="outline" disabled>
            <Spinner size="sm" className="mr-2" />
            Processando
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Spinner Customizado</CardTitle>
          <CardDescription>Com cores customizadas (sem conflito de animação)</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center space-x-4">
          <Spinner 
            size="lg" 
            className="text-blue-500" 
          />
          <Spinner 
            size="lg" 
            className="text-green-500" 
          />
          <Spinner 
            size="lg" 
            className="text-red-500" 
          />
          <Spinner 
            size="lg" 
            className="text-purple-500" 
          />
        </CardContent>
      </Card>
    </div>
  )
} 