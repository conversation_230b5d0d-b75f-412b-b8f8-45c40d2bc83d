// Exportar configuração base da API
export { default as api } from './api';

// Exportar services
export { default as authService } from './auth';
export { default as githubService } from './github';
export { default as geminiService } from './gemini';

// Exportar tipos dos services
export type {
  LoginCredentials,
  AuthResponse,
  User
} from './auth';

export type {
  GitHubRepository,
  GitHubCommit,
  GitHubUser
} from './github';

export type {
  PostGenerationRequest,
  PostGenerationResponse,
  PostTemplate
} from './gemini'; 