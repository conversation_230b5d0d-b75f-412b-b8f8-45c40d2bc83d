import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AnimatedCounter } from '@/components/ui/animated-counter';
import { TestimonialCard } from '@/components/ui/testimonial-card';
import { Check, Github, Sparkles, Zap, Users, TrendingUp, Clock, Shield, ArrowRight, Star, ChevronDown, Mail, Twitter, Linkedin, Code, Rocket, Heart, Target, Globe, Database, Cpu, Wifi } from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO from '@/components/SEO';

export default function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [hoveredPlan, setHoveredPlan] = useState<string | null>(null);
  const [isYearly, setIsYearly] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [hoveredStep, setHoveredStep] = useState<string | null>(null);
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);
  const [hoveredFaq, setHoveredFaq] = useState<number | null>(null);
  const [hoveredStat, setHoveredStat] = useState<number | null>(null);

  // Mouse tracking para efeitos interativos - CORRIGIDO PARA SCROLL
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      // Usar requestAnimationFrame para performance suave
      requestAnimationFrame(() => {
        // Adicionar scroll position para manter posição correta
        setMousePosition({
          x: e.clientX,
          y: e.clientY + window.scrollY
        });
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll tracking para ícones flutuantes
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      <SEO />
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        {/* Background Elements - Igual às outras páginas */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>

          {/* Elemento que segue o mouse - SEM DELAY */}
          <div
            className="absolute w-96 h-96 bg-gradient-to-r from-blue-500/8 to-purple-500/8 rounded-full blur-3xl pointer-events-none"
            style={{
              transform: `translate(${mousePosition.x - 192}px, ${mousePosition.y - 192}px)`,
              transition: 'none', // Remove transition para resposta instantânea
            }}
          ></div>

          {/* Segundo elemento com delay menor para efeito em camadas */}
          <div
            className="absolute w-64 h-64 bg-gradient-to-r from-cyan-500/6 to-pink-500/6 rounded-full blur-2xl pointer-events-none"
            style={{
              transform: `translate(${mousePosition.x - 128}px, ${mousePosition.y - 128}px)`,
              transition: 'transform 0.1s ease-out',
            }}
          ></div>
        </div>

        {/* Floating Icons - Igual às outras páginas */}
        <div className="absolute inset-0 pointer-events-none z-10">
          <div
            className="absolute text-blue-400/20 animate-bounce transition-transform duration-1000 ease-out"
            style={{
              top: `${32 + scrollY * 0.1}px`,
              left: '32px',
              transform: `translateY(${Math.sin(scrollY * 0.01) * 10}px)`
            }}
          >
            <Github size={24} />
          </div>
          <div
            className="absolute text-purple-400/20 animate-bounce delay-300 transition-transform duration-1000 ease-out"
            style={{
              top: `${48 + scrollY * 0.15}px`,
              right: '48px',
              transform: `translateY(${Math.cos(scrollY * 0.01) * 15}px)`
            }}
          >
            <Sparkles size={24} />
          </div>
          <div
            className="absolute text-cyan-400/20 animate-bounce delay-700 transition-transform duration-1000 ease-out"
            style={{
              bottom: `${32 + scrollY * 0.12}px`,
              left: '48px',
              transform: `translateY(${Math.sin(scrollY * 0.015) * 12}px)`
            }}
          >
            <Zap size={24} />
          </div>
          <div
            className="absolute text-green-400/20 animate-bounce delay-1000 transition-transform duration-1000 ease-out"
            style={{
              top: `${256 + scrollY * 0.08}px`,
              right: '128px',
              transform: `translateY(${Math.sin(scrollY * 0.012) * 8}px)`
            }}
          >
            <TrendingUp size={24} />
          </div>
          <div
            className="absolute text-pink-400/20 animate-bounce delay-500 transition-transform duration-1000 ease-out"
            style={{
              bottom: `${192 + scrollY * 0.09}px`,
              right: '256px',
              transform: `translateY(${Math.cos(scrollY * 0.008) * 10}px)`
            }}
          >
            <Users size={24} />
          </div>

          {/* Novos ícones espalhados randomicamente por toda a página */}
          <div
            className="absolute text-orange-400/20 animate-bounce delay-200 transition-transform duration-1000 ease-out"
            style={{
              top: `${450 + scrollY * 0.11}px`,
              right: '15%',
              transform: `translateY(${Math.sin(scrollY * 0.013) * 14}px)`
            }}
          >
            <Rocket size={20} />
          </div>

          <div
            className="absolute text-red-400/20 animate-bounce delay-800 transition-transform duration-1000 ease-out"
            style={{
              top: `${800 + scrollY * 0.07}px`,
              left: '8%',
              transform: `translateY(${Math.cos(scrollY * 0.011) * 9}px)`
            }}
          >
            <Heart size={18} />
          </div>

          <div
            className="absolute text-yellow-400/20 animate-bounce delay-1200 transition-transform duration-1000 ease-out"
            style={{
              top: `${1200 + scrollY * 0.13}px`,
              right: '25%',
              transform: `translateY(${Math.sin(scrollY * 0.009) * 11}px)`
            }}
          >
            <Target size={22} />
          </div>

          <div
            className="absolute text-indigo-400/20 animate-bounce delay-600 transition-transform duration-1000 ease-out"
            style={{
              top: `${1600 + scrollY * 0.14}px`,
              left: '12%',
              transform: `translateY(${Math.cos(scrollY * 0.014) * 13}px)`
            }}
          >
            <Code size={20} />
          </div>

          <div
            className="absolute text-emerald-400/20 animate-bounce delay-400 transition-transform duration-1000 ease-out"
            style={{
              top: `${2000 + scrollY * 0.10}px`,
              right: '5%',
              transform: `translateY(${Math.sin(scrollY * 0.016) * 7}px)`
            }}
          >
            <Globe size={19} />
          </div>

          <div
            className="absolute text-violet-400/20 animate-bounce delay-900 transition-transform duration-1000 ease-out"
            style={{
              top: `${2400 + scrollY * 0.06}px`,
              left: '18%',
              transform: `translateY(${Math.cos(scrollY * 0.010) * 12}px)`
            }}
          >
            <Database size={21} />
          </div>

          <div
            className="absolute text-teal-400/20 animate-bounce delay-1100 transition-transform duration-1000 ease-out"
            style={{
              top: `${2800 + scrollY * 0.08}px`,
              right: '30%',
              transform: `translateY(${Math.sin(scrollY * 0.012) * 8}px)`
            }}
          >
            <Cpu size={18} />
          </div>

          <div
            className="absolute text-sky-400/20 animate-bounce delay-1300 transition-transform duration-1000 ease-out"
            style={{
              top: `${3200 + scrollY * 0.09}px`,
              left: '22%',
              transform: `translateY(${Math.cos(scrollY * 0.015) * 10}px)`
            }}
          >
            <Wifi size={20} />
          </div>
        </div>

      {/* Navigation */}
      <nav className="relative z-50 bg-slate-800/50 backdrop-blur-xl border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2 group">
              <div className="w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <img src="/svg/icon-32.svg" alt="Code2Post" className="w-8 h-8" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Code2Post</span>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-slate-300 hover:text-white transition-colors">Recursos</a>
              <a href="#pricing" className="text-slate-300 hover:text-white transition-colors">Preços</a>
              <a href="#faq" className="text-slate-300 hover:text-white transition-colors">FAQ</a>
              <Link to="/login" className="text-slate-300 hover:text-white transition-all duration-300 hover:scale-105 cursor-pointer">
                Login
              </Link>
              <Link to="/register">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-105 hover:shadow-lg cursor-pointer">
                  Começar Grátis
                </Button>
              </Link>
            </div>

            <button 
              className="md:hidden text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-slate-800 border-t border-slate-700">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <a href="#features" className="block px-3 py-2 text-slate-300 hover:text-white">Recursos</a>
              <a href="#pricing" className="block px-3 py-2 text-slate-300 hover:text-white">Preços</a>
              <a href="#faq" className="block px-3 py-2 text-slate-300 hover:text-white">FAQ</a>
              <Link to="/login" className="block px-3 py-2 text-slate-300 hover:text-white transition-all duration-300 hover:scale-105 cursor-pointer">
                Login
              </Link>
              <Link to="/register" className="block px-3 py-2">
                <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-105 cursor-pointer">
                  Começar Grátis
                </Button>
              </Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative z-20">
        <div className="max-w-7xl mx-auto text-center">
          <Badge className="mb-6 bg-blue-500/10 text-blue-400 border-blue-500/20">
            🚀 Agora em Beta - Junte-se a 1000+ desenvolvedores
          </Badge>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
            Transforme Seu
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Código </span>
            Em
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Conteúdo</span>
          </h1>

          <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Gere automaticamente posts envolventes para o LinkedIn a partir da sua atividade no GitHub.
            Deixe a IA mostrar sua jornada de desenvolvimento e expanda sua rede profissional.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link to="/register">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-4 h-auto cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                <Github className="w-5 h-5 mr-2" />
                Começar Grátis com GitHub
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="border-slate-600 text-slate-300 hover:bg-slate-800 hover:text-white text-lg px-8 py-4 h-auto cursor-pointer transform hover:scale-105 transition-all duration-300 hover:border-slate-500">
              <span className="mr-2">▶️</span>
              Ver Demo
            </Button>
          </div>

          <div className="flex flex-wrap justify-center items-center gap-8 text-slate-400 text-sm">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Teste grátis de 7 dias
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Sem cartão de crédito
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Cancele quando quiser
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 border-y border-slate-700/50 relative z-10">
        <div className="max-w-7xl mx-auto">
          <p className="text-center text-slate-400 mb-8">Confiado por desenvolvedores de</p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            <div className="text-2xl font-bold text-slate-500 hover:text-slate-400 transition-colors duration-300">Nubank</div>
            <div className="text-2xl font-bold text-slate-500 hover:text-slate-400 transition-colors duration-300">iFood</div>
            <div className="text-2xl font-bold text-slate-500 hover:text-slate-400 transition-colors duration-300">Stone</div>
            <div className="text-2xl font-bold text-slate-500 hover:text-slate-400 transition-colors duration-300">Mercado Livre</div>
            <div className="text-2xl font-bold text-slate-500 hover:text-slate-400 transition-colors duration-300">PagSeguro</div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div
              className="text-center"
              onMouseEnter={() => setHoveredStat(0)}
              onMouseLeave={() => setHoveredStat(null)}
            >
              <div className={`relative overflow-hidden backdrop-blur-xl border rounded-xl p-6 transition-all duration-500 transform ${
                hoveredStat === 0
                  ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg'
              }`}>
                {/* Efeito de brilho */}
                <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 transition-opacity duration-500 ${
                  hoveredStat === 0 ? 'opacity-100' : 'opacity-0'
                }`} />

                <div className={`text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2 relative z-10 transition-all duration-300 ${
                  hoveredStat === 0 ? 'scale-110' : ''
                }`}>
                  <AnimatedCounter end={1000} suffix="+" />
                </div>
                <p className={`transition-colors duration-300 relative z-10 ${
                  hoveredStat === 0 ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Desenvolvedores Ativos
                </p>
              </div>
            </div>

            <div
              className="text-center"
              onMouseEnter={() => setHoveredStat(1)}
              onMouseLeave={() => setHoveredStat(null)}
            >
              <div className={`relative overflow-hidden backdrop-blur-xl border rounded-xl p-6 transition-all duration-500 transform ${
                hoveredStat === 1
                  ? 'scale-105 bg-slate-800/80 border-purple-500/50 shadow-2xl shadow-purple-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg'
              }`}>
                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 transition-opacity duration-500 ${
                  hoveredStat === 1 ? 'opacity-100' : 'opacity-0'
                }`} />

                <div className={`text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 relative z-10 transition-all duration-300 ${
                  hoveredStat === 1 ? 'scale-110' : ''
                }`}>
                  <AnimatedCounter end={50000} suffix="+" />
                </div>
                <p className={`transition-colors duration-300 relative z-10 ${
                  hoveredStat === 1 ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Posts Gerados
                </p>
              </div>
            </div>

            <div
              className="text-center"
              onMouseEnter={() => setHoveredStat(2)}
              onMouseLeave={() => setHoveredStat(null)}
            >
              <div className={`relative overflow-hidden backdrop-blur-xl border rounded-xl p-6 transition-all duration-500 transform ${
                hoveredStat === 2
                  ? 'scale-105 bg-slate-800/80 border-green-500/50 shadow-2xl shadow-green-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg'
              }`}>
                <div className={`absolute inset-0 bg-gradient-to-r from-green-500/10 to-cyan-500/10 transition-opacity duration-500 ${
                  hoveredStat === 2 ? 'opacity-100' : 'opacity-0'
                }`} />

                <div className={`text-4xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent mb-2 relative z-10 transition-all duration-300 ${
                  hoveredStat === 2 ? 'scale-110' : ''
                }`}>
                  <AnimatedCounter end={300} suffix="%" />
                </div>
                <p className={`transition-colors duration-300 relative z-10 ${
                  hoveredStat === 2 ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Aumento no Engajamento
                </p>
              </div>
            </div>

            <div
              className="text-center"
              onMouseEnter={() => setHoveredStat(3)}
              onMouseLeave={() => setHoveredStat(null)}
            >
              <div className={`relative overflow-hidden backdrop-blur-xl border rounded-xl p-6 transition-all duration-500 transform ${
                hoveredStat === 3
                  ? 'scale-105 bg-slate-800/80 border-orange-500/50 shadow-2xl shadow-orange-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg'
              }`}>
                <div className={`absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 transition-opacity duration-500 ${
                  hoveredStat === 3 ? 'opacity-100' : 'opacity-0'
                }`} />

                <div className={`text-4xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-2 relative z-10 transition-all duration-300 ${
                  hoveredStat === 3 ? 'scale-110' : ''
                }`}>
                  <AnimatedCounter end={99} suffix="%" />
                </div>
                <p className={`transition-colors duration-300 relative z-10 ${
                  hoveredStat === 3 ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Satisfação dos Usuários
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Como Funciona
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Transforme sua atividade de programação em conteúdo profissional em apenas 3 passos simples
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card
              className={`relative overflow-hidden transition-all duration-500 transform text-center ${
                hoveredStep === 'step1'
                  ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredStep('step1')}
              onMouseLeave={() => setHoveredStep(null)}
            >
              {/* Efeito de brilho */}
              <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 transition-opacity duration-500 ${
                hoveredStep === 'step1' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className={`w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                  hoveredStep === 'step1' ? 'scale-110 shadow-lg shadow-blue-500/50' : ''
                }`}>
                  <Github className="w-8 h-8 text-white" />
                  {hoveredStep === 'step1' && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredStep === 'step1' ? 'text-blue-300' : 'text-white'
                }`}>
                  1. Conecte o GitHub
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredStep === 'step1' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Conecte sua conta GitHub com segurança. Analisamos seus commits, pull requests e atividade dos projetos.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform text-center ${
                hoveredStep === 'step2'
                  ? 'scale-105 bg-slate-800/80 border-purple-500/50 shadow-2xl shadow-purple-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredStep('step2')}
              onMouseLeave={() => setHoveredStep(null)}
            >
              {/* Efeito de brilho */}
              <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 transition-opacity duration-500 ${
                hoveredStep === 'step2' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className={`w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                  hoveredStep === 'step2' ? 'scale-110 shadow-lg shadow-purple-500/50' : ''
                }`}>
                  <Sparkles className="w-8 h-8 text-white" />
                  {hoveredStep === 'step2' && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-pink-500 animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredStep === 'step2' ? 'text-purple-300' : 'text-white'
                }`}>
                  2. IA Gera Conteúdo
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredStep === 'step2' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Nossa IA analisa suas mudanças de código e cria posts profissionais e envolventes para o LinkedIn automaticamente.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform text-center ${
                hoveredStep === 'step3'
                  ? 'scale-105 bg-slate-800/80 border-pink-500/50 shadow-2xl shadow-pink-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredStep('step3')}
              onMouseLeave={() => setHoveredStep(null)}
            >
              {/* Efeito de brilho */}
              <div className={`absolute inset-0 bg-gradient-to-r from-pink-500/10 to-red-500/10 transition-opacity duration-500 ${
                hoveredStep === 'step3' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className={`w-16 h-16 bg-gradient-to-r from-pink-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                  hoveredStep === 'step3' ? 'scale-110 shadow-lg shadow-pink-500/50' : ''
                }`}>
                  <TrendingUp className="w-8 h-8 text-white" />
                  {hoveredStep === 'step3' && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-400 to-red-500 animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredStep === 'step3' ? 'text-pink-300' : 'text-white'
                }`}>
                  3. Compartilhe e Cresça
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredStep === 'step3' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Revise, edite e publique seus posts. Construa sua marca profissional e expanda sua rede.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/30 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Recursos Poderosos
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Tudo que você precisa para mostrar sua jornada de desenvolvimento profissionalmente
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'ai'
                  ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('ai')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              {/* Efeito de brilho */}
              <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'ai' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <Zap className={`w-8 h-8 text-blue-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'ai' ? 'scale-110 text-blue-300' : ''
                  }`} />
                  {hoveredFeature === 'ai' && (
                    <div className="absolute inset-0 w-8 h-8 bg-blue-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'ai' ? 'text-blue-300' : 'text-white'
                }`}>
                  Conteúdo Gerado por IA
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'ai' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  IA avançada analisa seu código e cria posts envolventes e contextuais que destacam suas conquistas técnicas.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'github'
                  ? 'scale-105 bg-slate-800/80 border-purple-500/50 shadow-2xl shadow-purple-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('github')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              {/* Efeito de brilho */}
              <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'github' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <Github className={`w-8 h-8 text-purple-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'github' ? 'scale-110 text-purple-300' : ''
                  }`} />
                  {hoveredFeature === 'github' && (
                    <div className="absolute inset-0 w-8 h-8 bg-purple-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'github' ? 'text-purple-300' : 'text-white'
                }`}>
                  Integração com GitHub
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'github' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Conecta perfeitamente com seus repositórios GitHub para rastrear commits, pull requests e marcos do projeto.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'clock'
                  ? 'scale-105 bg-slate-800/80 border-green-500/50 shadow-2xl shadow-green-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('clock')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <div className={`absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'clock' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <Clock className={`w-8 h-8 text-green-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'clock' ? 'scale-110 text-green-300' : ''
                  }`} />
                  {hoveredFeature === 'clock' && (
                    <div className="absolute inset-0 w-8 h-8 bg-green-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'clock' ? 'text-green-300' : 'text-white'
                }`}>
                  Agendamento Inteligente
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'clock' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Agende posts para horários de engajamento ideal e mantenha presença profissional consistente.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'users'
                  ? 'scale-105 bg-slate-800/80 border-pink-500/50 shadow-2xl shadow-pink-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('users')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <div className={`absolute inset-0 bg-gradient-to-r from-pink-500/10 to-rose-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'users' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <Users className={`w-8 h-8 text-pink-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'users' ? 'scale-110 text-pink-300' : ''
                  }`} />
                  {hoveredFeature === 'users' && (
                    <div className="absolute inset-0 w-8 h-8 bg-pink-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'users' ? 'text-pink-300' : 'text-white'
                }`}>
                  Colaboração em Equipe
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'users' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Colabore com sua equipe para criar e aprovar conteúdo que representa sua organização.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'analytics'
                  ? 'scale-105 bg-slate-800/80 border-orange-500/50 shadow-2xl shadow-orange-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('analytics')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <div className={`absolute inset-0 bg-gradient-to-r from-orange-500/10 to-amber-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'analytics' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <TrendingUp className={`w-8 h-8 text-orange-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'analytics' ? 'scale-110 text-orange-300' : ''
                  }`} />
                  {hoveredFeature === 'analytics' && (
                    <div className="absolute inset-0 w-8 h-8 bg-orange-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'analytics' ? 'text-orange-300' : 'text-white'
                }`}>
                  Analytics e Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'analytics' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Acompanhe métricas de engajamento, alcance e crescimento para otimizar sua estratégia de conteúdo.
                </CardDescription>
              </CardContent>
            </Card>

            <Card
              className={`relative overflow-hidden transition-all duration-500 transform ${
                hoveredFeature === 'security'
                  ? 'scale-105 bg-slate-800/80 border-cyan-500/50 shadow-2xl shadow-cyan-500/20'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredFeature('security')}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <div className={`absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 transition-opacity duration-500 ${
                hoveredFeature === 'security' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="relative z-10">
                <div className="relative">
                  <Shield className={`w-8 h-8 text-cyan-400 mb-2 transition-all duration-300 ${
                    hoveredFeature === 'security' ? 'scale-110 text-cyan-300' : ''
                  }`} />
                  {hoveredFeature === 'security' && (
                    <div className="absolute inset-0 w-8 h-8 bg-cyan-400 rounded-full animate-ping opacity-30" />
                  )}
                </div>
                <CardTitle className={`transition-colors duration-300 ${
                  hoveredFeature === 'security' ? 'text-cyan-300' : 'text-white'
                }`}>
                  Privacidade e Segurança
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className={`transition-colors duration-300 ${
                  hoveredFeature === 'security' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Segurança de nível empresarial com autenticação OAuth e armazenamento de dados criptografado.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-6">
              Preços Simples e Transparentes
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto mb-8">
              Escolha o plano que se adapta às suas necessidades. Comece grátis, faça upgrade quando estiver pronto.
            </p>

            {/* Toggle Mensal/Anual */}
            <div className="flex flex-col items-center justify-center mb-8">
              <div className="flex items-center justify-center space-x-4 mb-2">
                <span className={`text-lg ${!isYearly ? 'text-white font-semibold' : 'text-slate-400'} transition-colors duration-300`}>
                  Mensal
                </span>
                <button
                  onClick={() => setIsYearly(!isYearly)}
                  className={`relative w-16 h-8 rounded-full transition-all duration-500 ease-in-out transform hover:scale-110 ${
                    isYearly
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg shadow-blue-500/30'
                      : 'bg-slate-600 hover:bg-slate-500'
                  }`}
                >
                  <div
                    className={`absolute top-1 w-6 h-6 bg-white rounded-full shadow-lg transition-all duration-500 ease-in-out ${
                      isYearly ? 'translate-x-9 scale-110' : 'translate-x-1 scale-100'
                    }`}
                  />
                </button>
                <span className={`text-lg ${isYearly ? 'text-white font-semibold' : 'text-slate-400'} transition-colors duration-300`}>
                  Anual
                </span>
              </div>
              {/* Badge abaixo do toggle */}
              <div className="h-6 flex items-center justify-center">
                <Badge className={`bg-green-500/20 text-green-400 border-green-500/30 transition-all duration-500 ease-in-out ${
                  isYearly ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
                }`}>
                  💰 Economize 20%
                </Badge>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Free Plan */}
            <Card
              className={`relative overflow-hidden transition-all duration-300 cursor-pointer ${
                hoveredPlan === 'free'
                  ? 'bg-slate-800/80 border-blue-500/50 shadow-xl shadow-blue-500/20 scale-105'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg hover:shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredPlan('free')}
              onMouseLeave={() => setHoveredPlan(null)}
            >
              {/* Efeito de brilho no hover */}
              <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 transition-opacity duration-500 ${
                hoveredPlan === 'free' ? 'opacity-100' : 'opacity-0'
              }`} />

              <CardHeader className="text-center relative z-10 pb-4">
                <CardTitle className={`text-xl transition-colors duration-300 ${
                  hoveredPlan === 'free' ? 'text-blue-300' : 'text-white'
                }`}>
                  Gratuito
                </CardTitle>
                <div className="text-3xl font-bold mt-2">
                  <span className="text-white">R$ 0</span>
                  <span className="text-base text-slate-400">/sempre</span>
                </div>
                <CardDescription className={`mt-1 text-sm transition-colors duration-300 ${
                  hoveredPlan === 'free' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Perfeito para começar
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 relative z-10 flex flex-col h-full">
                <div className="space-y-3 flex-1">
                  {[
                    { text: '5 posts por mês' },
                    { text: '1 repositório GitHub' },
                    { text: 'Templates básicos de IA' },
                    { text: 'Suporte da comunidade' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className={`w-4 h-4 transition-colors duration-300 ${
                        hoveredPlan === 'free' ? 'text-blue-400' : 'text-green-400'
                      }`} />
                      <span className={`text-sm transition-colors duration-300 ${
                        hoveredPlan === 'free' ? 'text-white' : 'text-slate-300'
                      }`}>
                        {feature.text}
                      </span>
                    </div>
                  ))}
                </div>
                <Link to="/register" className="block mt-auto">
                  <Button className={`w-full mt-4 transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-lg ${
                    hoveredPlan === 'free'
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white border-transparent hover:from-blue-700 hover:to-purple-700'
                      : 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`} variant={hoveredPlan === 'free' ? 'default' : 'outline'}>
                    Começar Grátis
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Pro Plan - DESTAQUE */}
            <Card
              className={`relative overflow-visible transition-all duration-300 cursor-pointer ${
                hoveredPlan === 'pro'
                  ? 'bg-gradient-to-b from-blue-900/80 to-purple-900/80 border-blue-400/80 shadow-xl shadow-blue-500/30 scale-105'
                  : 'bg-gradient-to-b from-blue-900/60 to-purple-900/60 border-blue-500/50 shadow-lg hover:shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredPlan('pro')}
              onMouseLeave={() => setHoveredPlan(null)}
            >
              {/* Badge Popular - CORRIGIDO */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1 text-xs font-semibold shadow-lg whitespace-nowrap">
                  ⭐ Mais Popular
                </Badge>
              </div>

              {/* Efeito de brilho animado */}
              <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 transition-opacity duration-500 ${
                hoveredPlan === 'pro' ? 'opacity-100' : 'opacity-60'
              }`} />

              {/* Partículas flutuantes */}
              <div className="absolute inset-0 overflow-hidden">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className={`absolute w-2 h-2 bg-blue-400/30 rounded-full transition-all duration-1000 ${
                      hoveredPlan === 'pro' ? 'animate-bounce' : ''
                    }`}
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${10 + i * 10}%`,
                      animationDelay: `${i * 0.2}s`
                    }}
                  />
                ))}
              </div>

              <CardHeader className="text-center relative z-10 pb-4">
                <CardTitle className={`text-xl transition-colors duration-300 ${
                  hoveredPlan === 'pro' ? 'text-blue-200' : 'text-white'
                }`}>
                  Pro
                </CardTitle>
                <div className="text-3xl font-bold mt-2">
                  <span className="bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">
                    R$ {isYearly ? '78' : '97'}
                  </span>
                  <span className="text-base text-slate-300">
                    /{isYearly ? 'ano' : 'mês'}
                  </span>
                </div>
                {isYearly && (
                  <div className="text-xs text-green-400 font-semibold">
                    💰 Economize R$ 228/ano
                  </div>
                )}
                <CardDescription className={`mt-1 text-sm transition-colors duration-300 ${
                  hoveredPlan === 'pro' ? 'text-blue-200' : 'text-slate-300'
                }`}>
                  Para desenvolvedores sérios
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 relative z-10 flex flex-col h-full">
                <div className="space-y-3 flex-1">
                  {[
                    { text: '50 posts por mês' },
                    { text: 'Repositórios ilimitados' },
                    { text: 'Templates avançados de IA' },
                    { text: 'Agendamento de posts' },
                    { text: 'Dashboard de analytics' },
                    { text: 'Suporte prioritário' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className={`w-4 h-4 transition-colors duration-300 ${
                        hoveredPlan === 'pro' ? 'text-blue-400' : 'text-green-400'
                      }`} />
                      <span className={`text-sm transition-colors duration-300 ${
                        hoveredPlan === 'pro' ? 'text-white' : 'text-slate-300'
                      }`}>
                        {feature.text}
                      </span>
                    </div>
                  ))}
                </div>
                <Link to="/register" className="block mt-auto">
                  <Button className={`w-full mt-4 transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-xl ${
                    hoveredPlan === 'pro'
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white border-transparent hover:from-blue-600 hover:to-purple-600 shadow-lg'
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  }`}>
                    Iniciar Teste Pro
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Team Plan */}
            <Card
              className={`relative overflow-hidden transition-all duration-300 cursor-pointer ${
                hoveredPlan === 'team'
                  ? 'bg-slate-800/80 border-purple-500/50 shadow-xl shadow-purple-500/20 scale-105'
                  : 'bg-slate-800/50 border-slate-700/50 shadow-lg hover:shadow-xl'
              } backdrop-blur-xl`}
              onMouseEnter={() => setHoveredPlan('team')}
              onMouseLeave={() => setHoveredPlan(null)}
            >
              <CardHeader className="text-center pb-4">
                <CardTitle className={`text-xl transition-colors duration-300 ${
                  hoveredPlan === 'team' ? 'text-purple-300' : 'text-white'
                }`}>
                  Equipe
                </CardTitle>
                <div className="text-3xl font-bold mt-2">
                  <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    R$ {isYearly ? '198' : '247'}
                  </span>
                  <span className="text-base text-slate-400">
                    /{isYearly ? 'ano' : 'mês'}
                  </span>
                </div>
                {isYearly && (
                  <div className="text-xs text-green-400 font-semibold">
                    💰 Economize R$ 588/ano
                  </div>
                )}
                <CardDescription className={`mt-1 text-sm transition-colors duration-300 ${
                  hoveredPlan === 'team' ? 'text-slate-200' : 'text-slate-300'
                }`}>
                  Para equipes e organizações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 relative z-10 flex flex-col h-full">
                <div className="space-y-3 flex-1">
                  {[
                    { text: 'Posts ilimitados' },
                    { text: 'Até 10 membros da equipe' },
                    { text: 'Templates personalizados de IA' },
                    { text: 'Colaboração em equipe' },
                    { text: 'Analytics avançados' },
                    { text: 'Suporte dedicado' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className={`w-4 h-4 transition-colors duration-300 ${
                        hoveredPlan === 'team' ? 'text-purple-400' : 'text-green-400'
                      }`} />
                      <span className={`text-sm transition-colors duration-300 ${
                        hoveredPlan === 'team' ? 'text-white' : 'text-slate-300'
                      }`}>
                        {feature.text}
                      </span>
                    </div>
                  ))}
                </div>
                <Link to="/register" className="block mt-auto">
                  <Button className={`w-full mt-4 transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-lg ${
                    hoveredPlan === 'team'
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white border-transparent hover:from-purple-700 hover:to-pink-700'
                      : 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`} variant={hoveredPlan === 'team' ? 'default' : 'outline'}>
                    Falar com Vendas
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials - SUPER INTERATIVO */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                O Que Desenvolvedores Dizem
              </h2>
            </div>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto mb-8">
              Junte-se a milhares de desenvolvedores que já estão expandindo sua presença profissional
            </p>

            {/* Estatísticas de satisfação */}
            <div className="flex justify-center items-center space-x-8 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-1">
                  <AnimatedCounter end={4.9} suffix="/5.0" />
                </div>
                <p className="text-slate-400 text-sm">Avaliação Média</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-1">
                  <AnimatedCounter end={98} suffix="%" />
                </div>
                <p className="text-slate-400 text-sm">Recomendariam</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-1">
                  <AnimatedCounter end={2500} suffix="+" />
                </div>
                <p className="text-slate-400 text-sm">Avaliações</p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <TestimonialCard
              name="Rafael Santos"
              role="Desenvolvedor Sênior"
              company="Nubank"
              content="O Code2Post transformou como compartilho meu trabalho. Meu engajamento no LinkedIn aumentou 300% em apenas 2 meses! A IA realmente entende o contexto dos meus commits."
              rating={5}
              avatar="RS"
              delay={0}
            />

            <TestimonialCard
              name="Ana Costa"
              role="Tech Lead"
              company="iFood"
              content="Finalmente, uma ferramenta que entende desenvolvedores. A IA cria posts que realmente fazem sentido e mostram minhas habilidades de forma autêntica."
              rating={5}
              avatar="AC"
              delay={200}
            />

            <TestimonialCard
              name="Lucas Oliveira"
              role="CTO"
              company="Stone"
              content="Mudou o jogo para nossa equipe. Passamos de postar uma vez por mês para conteúdo consistente e de qualidade que atrai os melhores talentos."
              rating={5}
              avatar="LO"
              delay={400}
            />
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section id="faq" className="py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Perguntas Frequentes
            </h2>
            <p className="text-xl text-slate-300">
              Tudo que você precisa saber sobre o Code2Post
            </p>
          </div>

          <div className="space-y-4">
            {[
              {
                question: "Como o Code2Post analisa minha atividade no GitHub?",
                answer: "O Code2Post conecta com segurança à sua conta GitHub usando OAuth e analisa seus commits, pull requests e atividade dos projetos. Nossa IA então gera conteúdo envolvente baseado no seu trabalho real de desenvolvimento, destacando suas conquistas técnicas e progresso."
              },
              {
                question: "Meu código e dados estão seguros?",
                answer: "Absolutamente. Usamos segurança de nível empresarial com autenticação OAuth, armazenamento de dados criptografado, e nunca armazenamos seu código fonte real. Analisamos apenas mensagens de commit, metadados do repositório e atividade pública para gerar conteúdo."
              },
              {
                question: "Posso editar os posts gerados antes de publicar?",
                answer: "Sim! Todo post gerado pode ser totalmente personalizado. Você pode editar o conteúdo, adicionar seus próprios insights, ajustar o tom e visualizar como ficará antes de publicar no LinkedIn."
              },
              {
                question: "Com que frequência devo postar no LinkedIn?",
                answer: "Recomendamos postar 2-3 vezes por semana para engajamento ideal. O recurso de agendamento do Code2Post ajuda você a manter consistência sem sobrecarregar sua rede."
              },
              {
                question: "E se eu não tenho muita atividade no GitHub?",
                answer: "O Code2Post funciona com qualquer nível de atividade. Mesmo pequenos commits, projetos de aprendizado ou contribuições para código aberto podem ser transformados em conteúdo valioso que mostra seu crescimento e jornada de aprendizado."
              },
              {
                question: "Posso usar o Code2Post para contas de equipe?",
                answer: "Sim! Nosso plano Equipe suporta múltiplos membros da equipe e criação colaborativa de conteúdo. Perfeito para equipes de engenharia, startups e organizações que querem mostrar seu trabalho técnico."
              }
            ].map((faq, index) => (
              <Card
                key={index}
                className={`relative overflow-hidden transition-all duration-500 transform cursor-pointer ${
                  hoveredFaq === index
                    ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                    : 'bg-slate-800/50 border-slate-700/50 shadow-lg hover:shadow-xl'
                } backdrop-blur-xl`}
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
                onMouseEnter={() => setHoveredFaq(index)}
                onMouseLeave={() => setHoveredFaq(null)}
              >
                {/* Efeito de brilho */}
                <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 transition-opacity duration-500 ${
                  hoveredFaq === index ? 'opacity-100' : 'opacity-0'
                }`} />

                <CardHeader className="transition-colors duration-300 relative z-10">
                  <div className="flex justify-between items-center w-full">
                    <CardTitle className={`text-left transition-colors duration-300 flex-1 ${
                      hoveredFaq === index ? 'text-blue-300' : 'text-white'
                    }`}>
                      {faq.question}
                    </CardTitle>
                    <ChevronDown
                      className={`w-5 h-5 transition-all duration-300 ml-4 flex-shrink-0 ${
                        openFaq === index ? 'rotate-180' : ''
                      } ${
                        hoveredFaq === index ? 'text-blue-400 scale-110' : 'text-slate-400'
                      }`}
                    />
                  </div>
                </CardHeader>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openFaq === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <CardContent className="pt-0 pb-6 relative z-10">
                    <p className={`leading-relaxed transition-colors duration-300 ${
                      hoveredFaq === index ? 'text-slate-200' : 'text-slate-300'
                    }`}>
                      {faq.answer}
                    </p>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section - ESTILO ORIGINAL MELHORADO */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-900/30 to-purple-900/30 relative z-10">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-6">
            Pronto para Transformar Seu Código em Conteúdo?
          </h2>
          <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Junte-se a milhares de desenvolvedores que já estão expandindo sua presença profissional com conteúdo gerado por IA.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8">
            <Link to="/register">
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-10 py-5 h-auto cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <Github className="w-6 h-6 mr-3" />
                Iniciar Teste Grátis
                <ArrowRight className="w-6 h-6 ml-3" />
              </Button>
            </Link>
            <Button
              variant="outline"
              size="lg"
              className="border-slate-600 text-slate-300 hover:bg-slate-800 hover:text-white text-lg px-8 py-5 h-auto cursor-pointer transform hover:scale-105 transition-all duration-300"
            >
              <span className="mr-2">▶️</span>
              Ver Demo
            </Button>
          </div>

          <div className="flex flex-wrap justify-center items-center gap-8 text-slate-400 text-sm">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Teste grátis de 7 dias
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Sem cartão de crédito
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-400" />
              Cancele quando quiser
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900/80 backdrop-blur-xl border-t border-slate-700/50 py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-1">
              <div className="flex items-center space-x-2 mb-4 group">
                <div className="w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <img src="/svg/icon-32.svg" alt="Code2Post" className="w-8 h-8" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Code2Post</span>
              </div>
              <p className="text-slate-400 mb-4 hover:text-slate-300 transition-colors duration-300">
                Transforme sua atividade no GitHub em conteúdo envolvente para o LinkedIn com IA.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-slate-400 hover:text-blue-400 transition-all duration-300 hover:scale-110">
                  <Twitter className="w-5 h-5" />
                </a>
                <a href="#" className="text-slate-400 hover:text-blue-400 transition-all duration-300 hover:scale-110">
                  <Linkedin className="w-5 h-5" />
                </a>
                <a href="#" className="text-slate-400 hover:text-purple-400 transition-all duration-300 hover:scale-110">
                  <Github className="w-5 h-5" />
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-4">Produto</h3>
              <ul className="space-y-2">
                <li><a href="#features" className="text-slate-400 hover:text-white transition-colors">Recursos</a></li>
                <li><a href="#pricing" className="text-slate-400 hover:text-white transition-colors">Preços</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Integrações</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-4">Empresa</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Sobre</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Carreiras</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Contato</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-4">Suporte</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Central de Ajuda</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Documentação</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Status</a></li>
                <li><a href="mailto:<EMAIL>" className="text-slate-400 hover:text-white transition-colors">
                  <Mail className="w-4 h-4 inline mr-1" />
                  Suporte
                </a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              © 2025 Code2Post. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link to="/privacy-policy" className="text-slate-400 hover:text-white text-sm transition-colors">Política de Privacidade</Link>
              <Link to="/terms-of-service" className="text-slate-400 hover:text-white text-sm transition-colors">Termos de Serviço</Link>
              <Link to="/cookie-policy" className="text-slate-400 hover:text-white text-sm transition-colors">Política de Cookies</Link>
            </div>
          </div>
        </div>
      </footer>
      </div>
    </>
  );
}
