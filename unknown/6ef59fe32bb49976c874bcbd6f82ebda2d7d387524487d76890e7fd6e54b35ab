import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Settings as SettingsIcon, 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Link, 
  Clock, 
  Globe,
  Github,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Save,
  Upload,
  Trash2,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Crown,
  CreditCard,
  X,
  Download
} from 'lucide-react';

interface UserSettings {
  profile: {
    name: string;
    email: string;
    bio: string;
    location: string;
    website: string;
    avatar: string;
    phone: string;
    timezone: string;
  };
  posting: {
    frequency: 'daily' | 'weekly' | 'custom';
    bestTimes: string[];
    autoPost: boolean;
    requireApproval: boolean;
    defaultHashtags: string[];
    postStyle: 'professional' | 'casual' | 'technical';
  };
  integrations: {
    github: {
      connected: boolean;
      username: string;
      lastSync: string;
    };
    linkedin: {
      connected: boolean;
      username: string;
      lastSync: string;
    };
    gemini: {
      connected: boolean;
      apiKey: string;
      model: string;
    };
  };
  notifications: {
    email: boolean;
    push: boolean;
    postPublished: boolean;
    weeklyReport: boolean;
    newFeatures: boolean;
    marketing: boolean;
  };
  privacy: {
    profilePublic: boolean;
    showEmail: boolean;
    showStats: boolean;
    allowIndexing: boolean;
  };
  subscription: {
    plan: 'free' | 'pro' | 'enterprise';
    status: 'active' | 'cancelled' | 'expired';
    nextBilling: string;
    features: string[];
  };
}

export default function Settings() {
  const { user, loading } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isSaving, setIsSaving] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    );
  }

  // Dados fake para demonstração
  const [settings, setSettings] = useState<UserSettings>({
    profile: {
      name: 'Gabriel Camarate',
      email: '<EMAIL>',
      bio: 'Desenvolvedor Full Stack apaixonado por criar soluções inovadoras. Criador do Code2Post.',
      location: 'São Paulo, Brasil',
      website: 'https://gabrielcamarate.dev',
      avatar: '/api/placeholder/120/120',
      phone: '+55 11 99999-9999',
      timezone: 'America/Sao_Paulo'
    },
    posting: {
      frequency: 'weekly',
      bestTimes: ['09:00', '14:00', '18:00'],
      autoPost: false,
      requireApproval: true,
      defaultHashtags: ['#coding', '#webdev', '#javascript', '#react'],
      postStyle: 'professional'
    },
    integrations: {
      github: {
        connected: true,
        username: 'gabrielcamarate',
        lastSync: '2025-01-26T14:30:00Z'
      },
      linkedin: {
        connected: true,
        username: 'gabriel-camarate',
        lastSync: '2025-01-26T14:25:00Z'
      },
      gemini: {
        connected: true,
        apiKey: 'AIzaSyC7K8...',
        model: 'gemini-pro'
      }
    },
    notifications: {
      email: true,
      push: true,
      postPublished: true,
      weeklyReport: true,
      newFeatures: true,
      marketing: false
    },
    privacy: {
      profilePublic: true,
      showEmail: false,
      showStats: true,
      allowIndexing: true
    },
    subscription: {
      plan: 'pro',
      status: 'active',
      nextBilling: '2025-02-26',
      features: [
        'Posts ilimitados',
        'Analytics avançado',
        'Timeline retroativa',
        'Suporte prioritário',
        'Integrações premium'
      ]
    }
  });

  const tabs = [
    { id: 'profile', label: 'Perfil', icon: User },
    { id: 'posting', label: 'Postagem', icon: Clock },
    { id: 'integrations', label: 'Integrações', icon: Link },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'privacy', label: 'Privacidade', icon: Shield },
    { id: 'subscription', label: 'Assinatura', icon: Crown }
  ];

  const handleSave = async () => {
    setIsSaving(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsSaving(false);
  };

  const handleDisconnect = (service: string) => {
    console.log('Desconectar:', service);
  };

  const handleConnect = (service: string) => {
    console.log('Conectar:', service);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            {/* Avatar */}
            <div className="flex items-center space-x-6">
              <Avatar className="w-24 h-24">
                <AvatarImage src={settings.profile.avatar} />
                <AvatarFallback className="bg-blue-500 text-white text-2xl">
                  {settings.profile.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col sm:flex-row gap-2 w-full">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 flex-1 sm:flex-none"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Alterar Foto</span>
                  <span className="sm:hidden">Alterar</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-400 hover:text-red-300 hover:bg-red-500/10 flex-1 sm:flex-none"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Remover</span>
                  <span className="sm:hidden">Del</span>
                </Button>
              </div>
            </div>

            {/* Profile Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-slate-300">Nome Completo</Label>
                <Input
                  value={settings.profile.name}
                  onChange={(e) => setSettings({
                    ...settings,
                    profile: { ...settings.profile, name: e.target.value }
                  })}
                  className="mt-1 bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label className="text-slate-300">Email</Label>
                <Input
                  value={settings.profile.email}
                  onChange={(e) => setSettings({
                    ...settings,
                    profile: { ...settings.profile, email: e.target.value }
                  })}
                  className="mt-1 bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div className="md:col-span-2">
                <Label className="text-slate-300">Bio</Label>
                <textarea
                  value={settings.profile.bio}
                  onChange={(e) => setSettings({
                    ...settings,
                    profile: { ...settings.profile, bio: e.target.value }
                  })}
                  rows={3}
                  className="mt-1 w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-white resize-none"
                />
              </div>

              <div>
                <Label className="text-slate-300">Localização</Label>
                <Input
                  value={settings.profile.location}
                  onChange={(e) => setSettings({
                    ...settings,
                    profile: { ...settings.profile, location: e.target.value }
                  })}
                  className="mt-1 bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label className="text-slate-300">Website</Label>
                <Input
                  value={settings.profile.website}
                  onChange={(e) => setSettings({
                    ...settings,
                    profile: { ...settings.profile, website: e.target.value }
                  })}
                  className="mt-1 bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>
          </div>
        );

      case 'posting':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-slate-300">Frequência de Posts</Label>
                <select
                  value={settings.posting.frequency}
                  onChange={(e) => setSettings({
                    ...settings,
                    posting: { ...settings.posting, frequency: e.target.value as any }
                  })}
                  className="mt-1 w-full bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white"
                >
                  <option value="daily">Diária</option>
                  <option value="weekly">Semanal</option>
                  <option value="custom">Personalizada</option>
                </select>
              </div>

              <div>
                <Label className="text-slate-300">Estilo dos Posts</Label>
                <select
                  value={settings.posting.postStyle}
                  onChange={(e) => setSettings({
                    ...settings,
                    posting: { ...settings.posting, postStyle: e.target.value as any }
                  })}
                  className="mt-1 w-full bg-slate-700 border border-slate-600 rounded px-3 py-2 text-white"
                >
                  <option value="professional">Profissional</option>
                  <option value="casual">Casual</option>
                  <option value="technical">Técnico</option>
                </select>
              </div>
            </div>

            {/* Toggles */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                <div>
                  <div className="text-white font-medium">Publicação Automática</div>
                  <div className="text-slate-400 text-sm">Posts são publicados automaticamente sem aprovação</div>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    posting: { ...settings.posting, autoPost: !settings.posting.autoPost }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.posting.autoPost ? 'bg-blue-600' : 'bg-slate-600'
                  }`}
                >
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.posting.autoPost ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
              </div>

              <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                <div>
                  <div className="text-white font-medium">Requer Aprovação</div>
                  <div className="text-slate-400 text-sm">Todos os posts precisam ser aprovados antes da publicação</div>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    posting: { ...settings.posting, requireApproval: !settings.posting.requireApproval }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.posting.requireApproval ? 'bg-blue-600' : 'bg-slate-600'
                  }`}
                >
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.posting.requireApproval ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
              </div>
            </div>

            {/* Default Hashtags */}
            <div>
              <Label className="text-slate-300">Hashtags Padrão</Label>
              <div className="mt-2 flex flex-wrap gap-2">
                {settings.posting.defaultHashtags.map((tag, index) => (
                  <Badge key={index} className="bg-blue-500/10 text-blue-400 border-blue-500/30">
                    {tag}
                  </Badge>
                ))}
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                  <Zap className="w-3 h-3 mr-1" />
                  Adicionar
                </Button>
              </div>
            </div>
          </div>
        );

      case 'integrations':
        return (
          <div className="space-y-6">
            {/* GitHub */}
            <Card className="bg-slate-700/30 border-slate-600/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-slate-800 rounded-full flex items-center justify-center">
                      <Github className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="text-white font-medium">GitHub</div>
                      <div className="text-slate-400 text-sm">
                        {settings.integrations.github.connected 
                          ? `Conectado como @${settings.integrations.github.username}`
                          : 'Não conectado'
                        }
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {settings.integrations.github.connected ? (
                      <>
                        <Badge className="bg-green-500/10 text-green-400 border-green-500/30">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Conectado
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDisconnect('github')}
                          className="border-red-600 text-red-400 hover:bg-red-600/10"
                        >
                          Desconectar
                        </Button>
                      </>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleConnect('github')}
                        className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700"
                      >
                        Conectar
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* LinkedIn */}
            <Card className="bg-slate-700/30 border-slate-600/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                      <Linkedin className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="text-white font-medium">LinkedIn</div>
                      <div className="text-slate-400 text-sm">
                        {settings.integrations.linkedin.connected 
                          ? `Conectado como @${settings.integrations.linkedin.username}`
                          : 'Não conectado'
                        }
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {settings.integrations.linkedin.connected ? (
                      <>
                        <Badge className="bg-green-500/10 text-green-400 border-green-500/30">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Conectado
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDisconnect('linkedin')}
                          className="border-red-600 text-red-400 hover:bg-red-600/10"
                        >
                          Desconectar
                        </Button>
                      </>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleConnect('linkedin')}
                        className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700"
                      >
                        Conectar
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Gemini API */}
            <Card className="bg-slate-700/30 border-slate-600/30">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                        <Zap className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-medium">Gemini AI</div>
                        <div className="text-slate-400 text-sm">
                          {settings.integrations.gemini.connected 
                            ? `Modelo: ${settings.integrations.gemini.model}`
                            : 'Configure sua API key'
                          }
                        </div>
                      </div>
                    </div>
                    <Badge className={settings.integrations.gemini.connected 
                      ? "bg-green-500/10 text-green-400 border-green-500/30"
                      : "bg-yellow-500/10 text-yellow-400 border-yellow-500/30"
                    }>
                      {settings.integrations.gemini.connected ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Conectado
                        </>
                      ) : (
                        <>
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Configurar
                        </>
                      )}
                    </Badge>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Input
                      type={showApiKey ? "text" : "password"}
                      value={settings.integrations.gemini.apiKey}
                      onChange={(e) => setSettings({
                        ...settings,
                        integrations: {
                          ...settings.integrations,
                          gemini: { ...settings.integrations.gemini, apiKey: e.target.value }
                        }
                      })}
                      placeholder="Cole sua API key do Gemini aqui"
                      className="flex-1 bg-slate-700 border-slate-600 text-white"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="text-slate-400 hover:text-white"
                    >
                      {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-4">
            {Object.entries({
              email: 'Notificações por Email',
              push: 'Notificações Push',
              postPublished: 'Post Publicado',
              weeklyReport: 'Relatório Semanal',
              newFeatures: 'Novas Funcionalidades',
              marketing: 'Marketing e Promoções'
            }).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                <div>
                  <div className="text-white font-medium">{label}</div>
                  <div className="text-slate-400 text-sm">
                    {key === 'email' && 'Receba notificações importantes por email'}
                    {key === 'push' && 'Notificações em tempo real no navegador'}
                    {key === 'postPublished' && 'Quando um post for publicado com sucesso'}
                    {key === 'weeklyReport' && 'Resumo semanal de performance'}
                    {key === 'newFeatures' && 'Novidades e atualizações da plataforma'}
                    {key === 'marketing' && 'Ofertas especiais e conteúdo promocional'}
                  </div>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      [key]: !settings.notifications[key as keyof typeof settings.notifications]
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.notifications[key as keyof typeof settings.notifications] ? 'bg-blue-600' : 'bg-slate-600'
                  }`}
                >
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.notifications[key as keyof typeof settings.notifications] ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
              </div>
            ))}
          </div>
        );

      case 'privacy':
        return (
          <div className="space-y-4">
            {Object.entries({
              profilePublic: 'Perfil Público',
              showEmail: 'Mostrar Email',
              showStats: 'Mostrar Estatísticas',
              allowIndexing: 'Permitir Indexação'
            }).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                <div>
                  <div className="text-white font-medium">{label}</div>
                  <div className="text-slate-400 text-sm">
                    {key === 'profilePublic' && 'Seu perfil será visível publicamente'}
                    {key === 'showEmail' && 'Mostrar seu email no perfil público'}
                    {key === 'showStats' && 'Exibir estatísticas de posts e engajamento'}
                    {key === 'allowIndexing' && 'Permitir que mecanismos de busca indexem seu perfil'}
                  </div>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    privacy: {
                      ...settings.privacy,
                      [key]: !settings.privacy[key as keyof typeof settings.privacy]
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.privacy[key as keyof typeof settings.privacy] ? 'bg-blue-600' : 'bg-slate-600'
                  }`}
                >
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.privacy[key as keyof typeof settings.privacy] ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
              </div>
            ))}
          </div>
        );

      case 'subscription':
        return (
          <div className="space-y-6">
            {/* Current Plan */}
            <Card className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/30">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 mb-4">
                  <div className="flex items-center space-x-3">
                    <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-400" />
                    <div>
                      <div className="text-white text-lg sm:text-xl font-bold capitalize">{settings.subscription.plan}</div>
                      <div className="text-slate-300 text-sm">Status: {settings.subscription.status}</div>
                    </div>
                  </div>
                  <Badge className="bg-green-500/10 text-green-400 border-green-500/30 w-fit">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Ativo
                  </Badge>
                </div>

                <div className="text-slate-300 mb-4 text-sm sm:text-base">
                  Próxima cobrança: {new Date(settings.subscription.nextBilling).toLocaleDateString('pt-BR')}
                </div>

                <div className="space-y-2">
                  <div className="text-white font-medium text-sm sm:text-base">Funcionalidades incluídas:</div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {settings.subscription.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Available Plans */}
            <div className="space-y-4">
              <h3 className="text-lg sm:text-xl font-semibold text-white">Planos Disponíveis</h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Free Plan */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-4 sm:p-6">
                    <div className="text-center space-y-4">
                      <div className="space-y-2">
                        <h4 className="text-lg font-bold text-white">Free</h4>
                        <div className="text-2xl font-bold text-slate-300">R$ 0<span className="text-sm font-normal">/mês</span></div>
                      </div>
                      <div className="space-y-2 text-left">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">5 posts por mês</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">1 repositório</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Analytics básico</span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        className="w-full border-slate-600 text-slate-300"
                        disabled={settings.subscription.plan === 'free'}
                      >
                        {settings.subscription.plan === 'free' ? 'Plano Atual' : 'Downgrade'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Pro Plan */}
                <Card className="bg-gradient-to-b from-purple-500/10 to-blue-500/10 border-purple-500/30 relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                      Mais Popular
                    </Badge>
                  </div>
                  <CardContent className="p-4 sm:p-6">
                    <div className="text-center space-y-4">
                      <div className="space-y-2">
                        <h4 className="text-lg font-bold text-white">Pro</h4>
                        <div className="text-2xl font-bold text-white">R$ 29<span className="text-sm font-normal">/mês</span></div>
                      </div>
                      <div className="space-y-2 text-left">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Posts ilimitados</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Repositórios ilimitados</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Analytics avançado</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Agendamento automático</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">IA Assistant</span>
                        </div>
                      </div>
                      <Button
                        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        disabled={settings.subscription.plan === 'pro'}
                      >
                        {settings.subscription.plan === 'pro' ? 'Plano Atual' : 'Upgrade para Pro'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Enterprise Plan */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-4 sm:p-6">
                    <div className="text-center space-y-4">
                      <div className="space-y-2">
                        <h4 className="text-lg font-bold text-white">Enterprise</h4>
                        <div className="text-2xl font-bold text-slate-300">R$ 99<span className="text-sm font-normal">/mês</span></div>
                      </div>
                      <div className="space-y-2 text-left">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Tudo do Pro</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Múltiplas contas</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">API personalizada</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-slate-300 text-sm">Suporte prioritário</span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        className="w-full border-slate-600 text-slate-300"
                        disabled={settings.subscription.plan === 'enterprise'}
                      >
                        {settings.subscription.plan === 'enterprise' ? 'Plano Atual' : 'Upgrade para Enterprise'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Billing Actions */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                size="sm"
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 flex-1 sm:flex-none"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Gerenciar Cobrança</span>
                <span className="sm:hidden">Cobrança</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700 flex-1 sm:flex-none"
              >
                <Download className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Baixar Faturas</span>
                <span className="sm:hidden">Faturas</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-red-400 hover:text-red-300 hover:bg-red-500/10 flex-1 sm:flex-none"
              >
                <X className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Cancelar Assinatura</span>
                <span className="sm:hidden">Cancelar</span>
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <DashboardLayout>
      <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent leading-tight">
              Configurações
            </h1>
            <p className="text-slate-300 mt-1 sm:mt-2 text-sm sm:text-base">
              Gerencie suas preferências e configurações da conta
            </p>
          </div>

          <Button
            onClick={handleSave}
            disabled={isSaving}
            size="sm"
            className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700 w-full sm:w-auto"
          >
            <Save className={`w-4 h-4 mr-2 ${isSaving ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">{isSaving ? 'Salvando...' : 'Salvar Alterações'}</span>
            <span className="sm:hidden">{isSaving ? 'Salvando...' : 'Salvar'}</span>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Sidebar */}
          <Card className="lg:col-span-1 bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardContent className="p-3 sm:p-4">
              <nav className="space-y-1 sm:space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-lg transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-gradient-to-r from-blue-600/20 to-purple-600/20 text-blue-300 border border-blue-500/30'
                          : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      }`}
                    >
                      <Icon className="w-4 h-4 mr-2 sm:mr-3 flex-shrink-0" />
                      <span className="truncate">{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>

          {/* Content */}
          <Card className="lg:col-span-3 bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                {(() => {
                  const activeTabData = tabs.find(t => t.id === activeTab);
                  const Icon = activeTabData?.icon || SettingsIcon;
                  return (
                    <>
                      <Icon className="w-5 h-5 mr-2 text-purple-400" />
                      {activeTabData?.label}
                    </>
                  );
                })()}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
