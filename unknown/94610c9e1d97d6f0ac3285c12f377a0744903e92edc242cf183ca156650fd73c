import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Eye, 
  Edit3, 
  Check, 
  X, 
  ThumbsUp, 
  MessageCircle, 
  Share, 
  MoreHorizontal,
  Calendar,
  TrendingUp,
  Users,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

interface PostData {
  id: string;
  week: number;
  day: string;
  date: string;
  title: string;
  content: string;
  commits: string[];
  type: 'milestone' | 'feature' | 'bugfix' | 'update';
  estimatedEngagement: number;
  hashtags: string[];
  status: 'pending' | 'approved' | 'rejected' | 'editing';
  metrics: {
    expectedLikes: number;
    expectedComments: number;
    expectedShares: number;
    reachEstimate: number;
  };
}

interface PostPreviewProps {
  posts: PostData[];
  onPostApprove: (postId: string) => void;
  onPostReject: (postId: string) => void;
  onPostEdit: (postId: string, newContent: string) => void;
  onAllApproved: () => void;
}

export default function PostPreview({ 
  posts, 
  onPostApprove, 
  onPostReject, 
  onPostEdit, 
  onAllApproved 
}: PostPreviewProps) {
  const [currentPostIndex, setCurrentPostIndex] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState('');

  // Dados fake para demonstração (COMENTADO PARA TESTES REAIS)
  // const mockPosts: PostData[] = [...];

  // Usar posts reais passados como prop
  const currentPost = posts[currentPostIndex] || posts[0];
  const approvedCount = posts.filter(p => p.status === 'approved').length;
  const totalPosts = posts.length;

  const handleEdit = () => {
    setIsEditing(true);
    setEditContent(currentPost.content);
  };

  const handleSaveEdit = () => {
    onPostEdit(currentPost.id, editContent);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent('');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone': return 'bg-purple-500/10 text-purple-400 border-purple-500/30';
      case 'feature': return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      case 'bugfix': return 'bg-red-500/10 text-red-400 border-red-500/30';
      case 'update': return 'bg-green-500/10 text-green-400 border-green-500/30';
      default: return 'bg-slate-500/10 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header com Progresso */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white flex items-center">
                <Eye className="w-5 h-5 mr-2 text-purple-400" />
                Preview dos Posts
              </CardTitle>
              <CardDescription className="text-slate-300">
                Revise e aprove cada post antes da publicação
              </CardDescription>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-white">{approvedCount}/{totalPosts}</div>
              <div className="text-sm text-slate-400">Posts aprovados</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Navegação entre Posts */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentPostIndex(Math.max(0, currentPostIndex - 1))}
          disabled={currentPostIndex === 0}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Post Anterior
        </Button>

        <div className="flex items-center space-x-2">
          <Badge className={getTypeColor(currentPost.type)}>
            {currentPost.type}
          </Badge>
          <span className="text-slate-300">
            Post {currentPostIndex + 1} de {totalPosts}
          </span>
        </div>

        {/* <Button
          variant="outline"
          onClick={() => setCurrentPostIndex(Math.min(mockPosts.length - 1, currentPostIndex + 1))}
          disabled={currentPostIndex === mockPosts.length - 1}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          Próximo Post
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button> */}
      </div>

      {/* Preview do Post */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* LinkedIn Preview */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-white text-lg">Preview LinkedIn</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Simulação do LinkedIn */}
            <div className="bg-white rounded-lg p-4 text-black">
              {/* Header do Post */}
              <div className="flex items-start space-x-3 mb-3">
                <Avatar className="w-12 h-12">
                  <AvatarImage src="/api/placeholder/48/48" />
                  <AvatarFallback className="bg-blue-500 text-white">GC</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-semibold">Gabriel Camarate</div>
                  <div className="text-sm text-gray-600">Desenvolvedor Full Stack • 1º</div>
                  <div className="text-xs text-gray-500 flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {currentPost.date} • 🌍
                  </div>
                </div>
                <MoreHorizontal className="w-5 h-5 text-gray-400" />
              </div>

              {/* Conteúdo do Post */}
              <div className="mb-4">
                {isEditing ? (
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full h-64 p-3 border border-gray-300 rounded-lg resize-none text-sm"
                    placeholder="Edite o conteúdo do post..."
                  />
                ) : (
                  <div className="text-sm whitespace-pre-line leading-relaxed">
                    {currentPost.content}
                  </div>
                )}
              </div>

              {/* Hashtags */}
              <div className="flex flex-wrap gap-1 mb-4">
                {currentPost.hashtags.map((tag, index) => (
                  <span key={index} className="text-blue-600 text-sm hover:underline cursor-pointer">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Interações */}
              <div className="border-t pt-3">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                  <span>{currentPost.metrics.expectedLikes} curtidas</span>
                  <span>{currentPost.metrics.expectedComments} comentários</span>
                </div>
                
                <div className="flex items-center justify-between border-t pt-2">
                  <button className="flex items-center space-x-2 text-gray-600 hover:bg-gray-100 px-3 py-2 rounded">
                    <ThumbsUp className="w-4 h-4" />
                    <span className="text-sm">Curtir</span>
                  </button>
                  <button className="flex items-center space-x-2 text-gray-600 hover:bg-gray-100 px-3 py-2 rounded">
                    <MessageCircle className="w-4 h-4" />
                    <span className="text-sm">Comentar</span>
                  </button>
                  <button className="flex items-center space-x-2 text-gray-600 hover:bg-gray-100 px-3 py-2 rounded">
                    <Share className="w-4 h-4" />
                    <span className="text-sm">Compartilhar</span>
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Métricas e Ações */}
        <div className="space-y-6">
          {/* Métricas Previstas */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white text-lg flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-green-400" />
                Métricas Previstas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-blue-400">{currentPost.metrics.expectedLikes}</div>
                  <div className="text-xs text-slate-400">Curtidas</div>
                </div>
                <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-green-400">{currentPost.metrics.expectedComments}</div>
                  <div className="text-xs text-slate-400">Comentários</div>
                </div>
                <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-purple-400">{currentPost.metrics.expectedShares}</div>
                  <div className="text-xs text-slate-400">Compartilhamentos</div>
                </div>
                <div className="bg-slate-700/30 rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold text-orange-400">{currentPost.metrics.reachEstimate}</div>
                  <div className="text-xs text-slate-400">Alcance</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                <div className="flex items-center">
                  <Sparkles className="w-4 h-4 text-green-400 mr-2" />
                  <span className="text-sm text-green-400 font-medium">
                    {currentPost.estimatedEngagement}% de engajamento previsto
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Ações do Post */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-white text-lg">Ações</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {isEditing ? (
                  <div className="flex space-x-2">
                    <Button
                      onClick={handleSaveEdit}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <Check className="w-4 h-4 mr-2" />
                      Salvar
                    </Button>
                    <Button
                      onClick={handleCancelEdit}
                      variant="outline"
                      className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Cancelar
                    </Button>
                  </div>
                ) : (
                  <>
                    <Button
                      onClick={handleEdit}
                      variant="outline"
                      className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Edit3 className="w-4 h-4 mr-2" />
                      Editar Conteúdo
                    </Button>
                    
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => onPostApprove(currentPost.id)}
                        className="flex-1 bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Aprovar
                      </Button>
                      <Button
                        onClick={() => onPostReject(currentPost.id)}
                        variant="outline"
                        className="flex-1 border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Rejeitar
                      </Button>
                    </div>
                  </>
                )}

                <div className="pt-3 border-t border-slate-600/30">
                  <div className="text-xs text-slate-400 mb-2">Agendado para:</div>
                  <div className="text-sm text-white">{currentPost.day}, {currentPost.date}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Ações Finais */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="text-slate-300">
              {approvedCount === totalPosts ? (
                <span className="text-green-400 flex items-center">
                  <Check className="w-4 h-4 mr-2" />
                  Todos os posts foram aprovados!
                </span>
              ) : (
                <span>
                  {approvedCount} de {totalPosts} posts aprovados
                </span>
              )}
            </div>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <Pause className="w-4 h-4 mr-2" />
                Salvar Progresso
              </Button>
              
              <Button
                onClick={onAllApproved}
                disabled={approvedCount !== totalPosts}
                className="bg-gradient-to-r from-green-600 to-cyan-600 hover:from-green-700 hover:to-cyan-700 disabled:opacity-50"
              >
                <Play className="w-4 h-4 mr-2" />
                Ativar Timeline
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
