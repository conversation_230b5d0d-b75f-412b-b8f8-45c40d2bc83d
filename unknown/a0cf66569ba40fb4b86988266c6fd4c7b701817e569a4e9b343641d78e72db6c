import csrf from 'csrf';

// Configuração do CSRF
const tokens = new csrf();

/**
 * Middleware para gerar token CSRF
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next function
 */
export function generateCSRFToken(req, res, next) {
  try {
    // Gerar secret único para a sessão
    const secret = tokens.secretSync();

    // Gerar token CSRF
    const token = tokens.create(secret);

    // Armazenar secret na sessão (em produção, usar Redis ou banco)
    req.session = req.session || {};
    req.session.csrfSecret = secret;

    // Adicionar token ao response
    res.locals.csrfToken = token;

    // Adicionar token ao header para facilitar acesso no frontend
    res.setHeader('X-CSRF-Token', token);

    next();
  } catch (error) {
    console.error('Erro ao gerar token CSRF:', error);
    res.status(500).json({
      error: 'Erro interno ao gerar token de segurança',
      error_en: 'Internal error generating security token',
    });
  }
}

/**
 * Middleware para validar token CSRF
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next function
 */
export function validateCSRFToken(req, res, next) {
  try {
    // Métodos que não precisam de validação CSRF
    const safeMethods = ['GET', 'HEAD', 'OPTIONS'];

    if (safeMethods.includes(req.method)) {
      return next();
    }

    // Obter secret da sessão
    const secret = req.session?.csrfSecret;

    if (!secret) {
      return res.status(403).json({
        error: 'Token de segurança não encontrado. Faça login novamente.',
        error_en: 'Security token not found. Please login again.',
      });
    }

    // Obter token do header ou body
    const token = req.headers['x-csrf-token'] || req.body._csrf;

    if (!token) {
      return res.status(403).json({
        error: 'Token CSRF não fornecido',
        error_en: 'CSRF token not provided',
      });
    }

    // Validar token
    if (!tokens.verify(secret, token)) {
      return res.status(403).json({
        error: 'Token CSRF inválido',
        error_en: 'Invalid CSRF token',
      });
    }

    next();
  } catch (error) {
    console.error('Erro na validação CSRF:', error);
    res.status(500).json({
      error: 'Erro interno na validação de segurança',
      error_en: 'Internal error in security validation',
    });
  }
}

/**
 * Middleware para obter token CSRF (para rotas que precisam do token)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next function
 */
export function getCSRFToken(req, res, next) {
  try {
    const secret = req.session?.csrfSecret;

    if (secret) {
      const token = tokens.create(secret);
      res.json({ csrfToken: token });
    } else {
      res.status(404).json({
        error: 'Sessão não encontrada',
        error_en: 'Session not found',
      });
    }
  } catch (error) {
    console.error('Erro ao obter token CSRF:', error);
    res.status(500).json({
      error: 'Erro interno ao obter token de segurança',
      error_en: 'Internal error getting security token',
    });
  }
}
