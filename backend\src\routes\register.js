import express from 'express';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import userService from '../services/userService.js';

const router = express.Router();

// Middleware para tratar erros de validação
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
      })),
    });
  }
  next();
};

// Lista de senhas comuns (em produção, seria um arquivo separado)
const senhasComuns = [
  '123456',
  'password',
  '123456789',
  '12345678',
  '12345',
  'qwerty',
  'abc123',
  'football',
  '1234567',
  'monkey',
  '111111',
  'letmein',
  '1234',
  '1234567890',
  'dragon',
  'baseball',
  'sunshine',
  'iloveyou',
  'trustno1',
  'princess',
  'admin',
  'welcome',
  'solo',
  'master',
  'hello',
  'freedom',
  'whatever',
  'qazwsx',
  'ninja',
  'michael',
];

// Validações para registro
const validarRegistro = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres')
    .matches(/^[a-zA-ZÀ-ÿ\s]+$/)
    .withMessage('Nome deve conter apenas letras e espaços'),

  body('email')
    .isEmail()
    .withMessage('Email deve ter formato válido')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email muito longo'),

  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Senha deve ter entre 8 e 128 caracteres')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage(
      'Senha deve conter: 1 minúscula, 1 maiúscula, 1 número e 1 símbolo (@$!%*?&)'
    )
    .custom(value => {
      // Verificar se não é uma senha comum
      if (senhasComuns.includes(value.toLowerCase())) {
        throw new Error('Senha muito comum, escolha uma senha mais segura');
      }
      return true;
    }),

  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('Confirmação de senha não confere');
    }
    return true;
  }),

  handleValidationErrors,
];

// Rota de registro
router.post('/', validarRegistro, async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Criar usuário usando o serviço
    const userData = {
      name: name.trim(),
      email: email.toLowerCase(),
      password: password,
    };

    const newUser = await userService.createUser(userData);

    // Gerar tokens JWT
    const accessToken = jwt.sign(
      {
        userId: newUser.id,
        email: newUser.email,
        name: newUser.name,
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      {
        userId: newUser.id,
        type: 'refresh',
      },
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    console.log(`✅ Usuário registrado com sucesso: ${newUser.email}`);

    res.status(201).json({
      message: 'Conta criada com sucesso!',
      accessToken,
      refreshToken,
      user: newUser,
    });

  } catch (error) {
    console.error('❌ Erro no registro:', error.message);
    
    if (error.message.includes('Email já está em uso')) {
      return res.status(409).json({ 
        error: 'Email já está em uso. Tente fazer login ou use outro email.',
      });
    }

    res.status(400).json({ 
      error: error.message || 'Erro ao criar conta. Tente novamente.',
    });
  }
});

export default router; 