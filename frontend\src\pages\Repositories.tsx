import { useState, useEffect, useCallback } from 'react';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  GitBranch,
  Star,
  GitFork,
  Calendar,
  Search,
  RefreshCw,
  ExternalLink,
  Lock,
  Globe,
  AlertCircle,
  Loader2,
  Code,
  Settings,
  MessageSquare,
  Activity,
  Users,
  Clock,
  X,
  TrendingUp,
  Eye,
  Download,
  Share2,
  Heart,
  Filter,
  ArrowUpDown,
  ChevronDown
} from 'lucide-react';
// import githubService from '@/services/github'; // 🎭 REMOVIDO - USANDO DADOS FICTÍCIOS
import type { GitHubRepository } from '@/types';
import { toast } from 'sonner';

// CSS personalizado para animações
const customStyles = `
  .animation-delay-150 {
    animation-delay: 150ms;
  }
  .border-3 {
    border-width: 3px;
  }
`;

export default function Repositories() {
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [filteredRepos, setFilteredRepos] = useState<GitHubRepository[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  const [filterType, setFilterType] = useState<'all' | 'public' | 'private' | 'fork' | 'source' | 'favorites' | 'stars'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'stars' | 'forks' | 'alphabetical'>('recent');
  const [selectedRepository, setSelectedRepository] = useState<GitHubRepository | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCommitsModalOpen, setIsCommitsModalOpen] = useState(false);
  const [commits, setCommits] = useState<any[]>([]);
  const [commitsLoading, setCommitsLoading] = useState(false);
  const [commitsPage, setCommitsPage] = useState(1);
  const [commitsPerPage, setCommitsPerPage] = useState(10);
  const [commitsTotalPages, setCommitsTotalPages] = useState(1);
  const [recentCommits, setRecentCommits] = useState<any[]>([]);
  const [recentCommitsLoading, setRecentCommitsLoading] = useState(false);
  const [recentCommitsPage, setRecentCommitsPage] = useState(1);
  const [recentCommitsTotalPages, setRecentCommitsTotalPages] = useState(1);
  const [selectedCommit, setSelectedCommit] = useState<any>(null);
  const [isCommitDetailsModalOpen, setIsCommitDetailsModalOpen] = useState(false);
  const [branches, setBranches] = useState<any[]>([]);
  const [branchesLoading, setBranchesLoading] = useState(false);

  const [favoriteRepos, setFavoriteRepos] = useState<Set<number>>(new Set());

  const [loadingDetails, setLoadingDetails] = useState<Set<number>>(new Set());
  const [activeFilters, setActiveFilters] = useState<Set<string>>(new Set());
  const [loadingProgress, setLoadingProgress] = useState<{ current: number, total: number } | null>(null);
  const [loadingRepos, setLoadingRepos] = useState<Set<number>>(new Set());
  const [retryCount, setRetryCount] = useState(0);
  const [lastRefreshTime, setLastRefreshTime] = useState(0);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [visibleRepos, setVisibleRepos] = useState<Set<number>>(new Set()); // Repositórios que devem aparecer

  // Cache para evitar requisições desnecessárias
  const [dataCache, setDataCache] = useState<Map<string, any>>(new Map());

  // Rate limiter para controlar requisições
  const [rateLimitState, setRateLimitState] = useState({
    requestCount: 0,
    windowStart: Date.now(),
    isLimited: false
  });

  // Função utilitária para retry com backoff exponencial otimizada
  const retryWithBackoff = async (fn: () => Promise<any>, maxRetries: number = 2, baseDelay: number = 2000) => {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        if (attempt === maxRetries) {
          throw error; // Última tentativa, propagar erro
        }

        if (error?.response?.status === 429) {
          const delay = baseDelay * Math.pow(2, attempt); // 2s, 4s
          console.log(`⏳ Rate limit atingido. Tentativa ${attempt + 1}/${maxRetries + 1}. Aguardando ${delay}ms...`);
          toast.warning(`Rate limit atingido. Aguardando ${Math.ceil(delay / 1000)}s...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else if (error?.message?.includes('CORS') || error?.code === 'ERR_CORS' || error?.response?.status === 0) {
          const delay = baseDelay * Math.pow(1.5, attempt);
          console.log(`🔄 Erro CORS detectado. Tentativa ${attempt + 1}/${maxRetries + 1}. Aguardando ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else if (error?.code === 'ERR_NETWORK') {
          const delay = baseDelay * Math.pow(1.5, attempt);
          console.log(`🌐 Erro de rede. Tentativa ${attempt + 1}/${maxRetries + 1}. Aguardando ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          throw error; // Erro diferente, propagar imediatamente
        }
      }
    }
  };

  // Rate limiter inteligente
  const checkRateLimit = async () => {
    const now = Date.now();
    const windowDuration = 60000; // 1 minuto
    const maxRequestsPerWindow = 50; // Limite conservador

    // Reset window se passou 1 minuto
    if (now - rateLimitState.windowStart > windowDuration) {
      setRateLimitState({
        requestCount: 0,
        windowStart: now,
        isLimited: false
      });
    }

    // Se atingiu o limite, aguardar
    if (rateLimitState.requestCount >= maxRequestsPerWindow) {
      const waitTime = windowDuration - (now - rateLimitState.windowStart);
      if (waitTime > 0) {
        console.log(`⏳ Rate limit preventivo. Aguardando ${Math.ceil(waitTime / 1000)}s...`);
        toast.info(`Aguardando ${Math.ceil(waitTime / 1000)}s para evitar rate limit...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        setRateLimitState({
          requestCount: 0,
          windowStart: Date.now(),
          isLimited: false
        });
      }
    }

    // Incrementar contador
    setRateLimitState(prev => ({
      ...prev,
      requestCount: prev.requestCount + 1
    }));
  };

  // Buscar repositórios ao carregar a página
  useEffect(() => {
    fetchRepositories();
  }, []);

  // Filtrar repositórios simples e direto
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredRepos(repositories);
    } else {
      const filtered = repositories.filter(repo =>
        repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.language?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRepos(filtered);
    }
  }, [repositories, searchTerm]);

  // 🎭 DADOS FICTÍCIOS PARA DEMONSTRAÇÃO
  const generateFakeRepositories = (): GitHubRepository[] => [
    {
      id: 1,
      name: 'code2post-frontend',
      full_name: 'gabrielcamarate/code2post-frontend',
      description: 'Frontend moderno em React + TypeScript para automação de posts do LinkedIn',
      private: false,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/code2post-frontend',
      clone_url: 'https://github.com/gabrielcamarate/code2post-frontend.git',
      stargazers_count: 42,
      forks_count: 8,
      language: 'TypeScript',
      updated_at: '2024-01-30T10:30:00Z',
      languages: [
        { name: 'TypeScript', bytes: 125000, percentage: '65.2%' },
        { name: 'CSS', bytes: 45000, percentage: '23.4%' },
        { name: 'JavaScript', bytes: 22000, percentage: '11.4%' }
      ],
      totalCommits: 156,
      branchesCount: 4,
      insights: {
        size: 2340,
        lastPush: '2024-01-30T10:30:00Z',
        views: 234,
        clones: 45,
        defaultBranch: 'main',
        openIssues: 3,
        watchers: 42
      }
    },
    {
      id: 2,
      name: 'ai-content-generator',
      full_name: 'gabrielcamarate/ai-content-generator',
      description: 'Sistema de geração de conteúdo usando IA para redes sociais',
      private: false,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/ai-content-generator',
      clone_url: 'https://github.com/gabrielcamarate/ai-content-generator.git',
      stargazers_count: 89,
      forks_count: 23,
      language: 'Python',
      updated_at: '2024-01-29T15:45:00Z',
      languages: [
        { name: 'Python', bytes: 89000, percentage: '78.5%' },
        { name: 'JavaScript', bytes: 15000, percentage: '13.2%' },
        { name: 'HTML', bytes: 9400, percentage: '8.3%' }
      ],
      totalCommits: 203,
      branchesCount: 6,
      insights: {
        size: 1890,
        lastPush: '2024-01-29T15:45:00Z',
        views: 456,
        clones: 78,
        defaultBranch: 'main',
        openIssues: 7,
        watchers: 89
      }
    },
    {
      id: 3,
      name: 'react-dashboard-template',
      full_name: 'gabrielcamarate/react-dashboard-template',
      description: 'Template moderno de dashboard com React, Tailwind CSS e componentes reutilizáveis',
      private: true,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/react-dashboard-template',
      clone_url: 'https://github.com/gabrielcamarate/react-dashboard-template.git',
      stargazers_count: 15,
      forks_count: 3,
      language: 'JavaScript',
      updated_at: '2024-01-28T09:20:00Z',
      languages: [
        { name: 'JavaScript', bytes: 67000, percentage: '55.8%' },
        { name: 'CSS', bytes: 34000, percentage: '28.3%' },
        { name: 'HTML', bytes: 19000, percentage: '15.9%' }
      ],
      totalCommits: 87,
      branchesCount: 3,
      insights: {
        size: 1456,
        lastPush: '2024-01-28T09:20:00Z',
        views: 123,
        clones: 12,
        defaultBranch: 'main',
        openIssues: 2,
        watchers: 15
      }
    },
    {
      id: 4,
      name: 'nodejs-api-boilerplate',
      full_name: 'gabrielcamarate/nodejs-api-boilerplate',
      description: 'Boilerplate completo para APIs REST com Node.js, Express e MongoDB',
      private: false,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/nodejs-api-boilerplate',
      clone_url: 'https://github.com/gabrielcamarate/nodejs-api-boilerplate.git',
      stargazers_count: 67,
      forks_count: 19,
      language: 'JavaScript',
      updated_at: '2024-01-27T14:10:00Z',
      languages: [
        { name: 'JavaScript', bytes: 78000, percentage: '89.1%' },
        { name: 'JSON', bytes: 6700, percentage: '7.7%' },
        { name: 'Dockerfile', bytes: 2800, percentage: '3.2%' }
      ],
      totalCommits: 134,
      branchesCount: 5,
      insights: {
        size: 987,
        lastPush: '2024-01-27T14:10:00Z',
        views: 345,
        clones: 56,
        defaultBranch: 'main',
        openIssues: 4,
        watchers: 67
      }
    },
    {
      id: 5,
      name: 'machine-learning-toolkit',
      full_name: 'gabrielcamarate/machine-learning-toolkit',
      description: 'Conjunto de ferramentas e algoritmos de machine learning em Python',
      private: false,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/machine-learning-toolkit',
      clone_url: 'https://github.com/gabrielcamarate/machine-learning-toolkit.git',
      stargazers_count: 156,
      forks_count: 34,
      language: 'Python',
      updated_at: '2024-01-26T11:30:00Z',
      languages: [
        { name: 'Python', bytes: 145000, percentage: '92.3%' },
        { name: 'Jupyter Notebook', bytes: 8900, percentage: '5.7%' },
        { name: 'Shell', bytes: 3100, percentage: '2.0%' }
      ],
      totalCommits: 289,
      branchesCount: 8,
      insights: {
        size: 3456,
        lastPush: '2024-01-26T11:30:00Z',
        views: 678,
        clones: 123,
        defaultBranch: 'main',
        openIssues: 12,
        watchers: 156
      }
    },
    {
      id: 6,
      name: 'mobile-app-flutter',
      full_name: 'gabrielcamarate/mobile-app-flutter',
      description: 'Aplicativo móvel multiplataforma desenvolvido com Flutter',
      private: true,
      fork: false,
      html_url: 'https://github.com/gabrielcamarate/mobile-app-flutter',
      clone_url: 'https://github.com/gabrielcamarate/mobile-app-flutter.git',
      stargazers_count: 28,
      forks_count: 5,
      language: 'Dart',
      updated_at: '2024-01-25T16:45:00Z',
      languages: [
        { name: 'Dart', bytes: 89000, percentage: '85.4%' },
        { name: 'Swift', bytes: 8900, percentage: '8.5%' },
        { name: 'Kotlin', bytes: 6300, percentage: '6.1%' }
      ],
      totalCommits: 167,
      branchesCount: 4,
      insights: {
        size: 2134,
        lastPush: '2024-01-25T16:45:00Z',
        views: 89,
        clones: 15,
        defaultBranch: 'main',
        openIssues: 6,
        watchers: 28
      }
    }
  ];

  const fetchRepositories = async (isRetry = false) => {
    try {
      setLoading(true);

      // 🎭 SIMULAR DELAY DE CARREGAMENTO
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 🎭 USAR DADOS FICTÍCIOS
      const fakeRepos = generateFakeRepositories();
      setRepositories(fakeRepos);
      setVisibleRepos(new Set(fakeRepos.map(r => r.id)));
      setRetryCount(0);

      toast.success(`${fakeRepos.length} repositórios carregados com sucesso!`);
    } catch (error: any) {
      console.error('Erro ao buscar repositórios:', error);
      toast.error('Erro ao carregar repositórios. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    const now = Date.now();
    const debounceTime = 2000; // 2 segundos de debounce

    if (now - lastRefreshTime < debounceTime) {
      toast.warning('Aguarde um momento antes de atualizar novamente');
      return;
    }

    setLastRefreshTime(now);
    fetchRepositories();
  };

  // Sistema otimizado de carregamento em lotes
  const loadAllRepositoriesCommits = async (repos: GitHubRepository[], signal?: AbortSignal) => {
    // Filtrar apenas repositórios que precisam de dados
    const reposToProcess = repos.filter(repo =>
      repo.totalCommits === undefined || repo.languages === undefined || repo.branchesCount === undefined || repo.insights === undefined
    );

    if (reposToProcess.length === 0) {
      // Tornar todos os repositórios visíveis imediatamente se não precisam de dados
      setVisibleRepos(new Set(repos.map(r => r.id)));
      return;
    }

    setLoadingProgress({ current: 0, total: reposToProcess.length });

    // Configuração de lotes
    const BATCH_SIZE = 3; // Processar 3 repositórios por vez
    const DELAY_BETWEEN_BATCHES = 3000; // 3 segundos entre lotes
    const DELAY_BETWEEN_REQUESTS = 800; // 800ms entre requisições individuais

    // Dividir em lotes
    const batches = [];
    for (let i = 0; i < reposToProcess.length; i += BATCH_SIZE) {
      batches.push(reposToProcess.slice(i, i + BATCH_SIZE));
    }

    let processedCount = 0;

    // Processar cada lote
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      if (signal?.aborted) {
        setLoadingProgress(null);
        return;
      }

      const batch = batches[batchIndex];
      console.log(`📦 Processando lote ${batchIndex + 1}/${batches.length} (${batch.length} repositórios)`);

      // Processar repositórios do lote em paralelo limitado
      const batchPromises = batch.map(async (repo, index) => {
        // Delay escalonado dentro do lote
        await new Promise(resolve => setTimeout(resolve, index * DELAY_BETWEEN_REQUESTS));

        if (signal?.aborted) return;

        // Tornar o repositório visível
        setVisibleRepos(prev => new Set(prev).add(repo.id));
        return await loadRepositoryData(repo);
      });

      // Aguardar conclusão do lote
      await Promise.allSettled(batchPromises);

      processedCount += batch.length;
      setLoadingProgress({ current: processedCount, total: reposToProcess.length });

      // Delay entre lotes (exceto no último)
      if (batchIndex < batches.length - 1) {
        const remainingBatches = batches.length - batchIndex - 1;
        console.log(`⏳ Aguardando ${DELAY_BETWEEN_BATCHES / 1000}s antes do próximo lote... (${remainingBatches} lotes restantes)`);
        toast.info(`Processando em lotes para evitar rate limit... ${remainingBatches} lotes restantes`);
        await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
      }
    }

    setLoadingProgress(null);
    console.log(`✅ Carregamento concluído: ${processedCount} repositórios processados`);
    toast.success(`Carregamento concluído! ${processedCount} repositórios processados com sucesso.`);
  };

  // 🎭 FUNÇÃO REMOVIDA - DADOS FICTÍCIOS JÁ INCLUEM TUDO
  const loadRepositoryData = async (repo: GitHubRepository) => {
    // 🎭 NÃO FAZ NADA - DADOS FICTÍCIOS JÁ ESTÃO COMPLETOS
    console.log(`📋 Dados fictícios já carregados para ${repo.name}`);
  };



  const getLanguageColor = (language: string | null) => {
    const colors: Record<string, string> = {
      'JavaScript': 'bg-yellow-500',
      'TypeScript': 'bg-blue-500',
      'Python': 'bg-green-500',
      'Java': 'bg-red-500',
      'C++': 'bg-purple-500',
      'C#': 'bg-indigo-500',
      'C': 'bg-gray-600',
      'PHP': 'bg-violet-500',
      'Ruby': 'bg-red-600',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-500',
      'Swift': 'bg-orange-600',
      'Kotlin': 'bg-purple-600',
      'HTML': 'bg-orange-400',
      'CSS': 'bg-blue-400',
      'SCSS': 'bg-pink-500',
      'Vue': 'bg-green-400',
      'React': 'bg-cyan-400',
      'Angular': 'bg-red-400',
      'Dart': 'bg-blue-300',
      'Shell': 'bg-gray-700',
      'PowerShell': 'bg-blue-700',
      'Dockerfile': 'bg-blue-800',
      'YAML': 'bg-red-300',
      'JSON': 'bg-yellow-600',
      'XML': 'bg-orange-300',
      'Markdown': 'bg-gray-400',
    };
    return colors[language || ''] || 'bg-gray-500';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Estatísticas dos repositórios
  const stats = {
    total: repositories.length,
    public: repositories.filter(r => !r.private).length,
    private: repositories.filter(r => r.private).length,
    forks: repositories.filter(r => r.fork).length,
    source: repositories.filter(r => !r.fork).length,
    stars: repositories.reduce((acc, repo) => acc + repo.stargazers_count, 0),
    favorites: favoriteRepos.size,
  };

  // Filtrar e ordenar repositórios
  useEffect(() => {
    let filtered = repositories;

    // Aplicar filtros múltiplos
    activeFilters.forEach(filter => {
      switch (filter) {
        case 'public':
          filtered = filtered.filter(repo => !repo.private);
          break;
        case 'private':
          filtered = filtered.filter(repo => repo.private);
          break;
        case 'fork':
          filtered = filtered.filter(repo => repo.fork);
          break;
        case 'source':
          filtered = filtered.filter(repo => !repo.fork);
          break;
        case 'favorites':
          filtered = filtered.filter(repo => favoriteRepos.has(repo.id));
          break;
        case 'stars':
          filtered = filtered.filter(repo => repo.stargazers_count > 0);
          break;
      }
    });

    // Aplicar filtro por tipo (compatibilidade com sistema antigo)
    if (filterType !== 'all' && !activeFilters.has(filterType)) {
      switch (filterType) {
        case 'public':
          filtered = filtered.filter(repo => !repo.private);
          break;
        case 'private':
          filtered = filtered.filter(repo => repo.private);
          break;
        case 'fork':
          filtered = filtered.filter(repo => repo.fork);
          break;
        case 'source':
          filtered = filtered.filter(repo => !repo.fork);
          break;
        case 'favorites':
          filtered = filtered.filter(repo => favoriteRepos.has(repo.id));
          break;
        case 'stars':
          filtered = filtered.filter(repo => repo.stargazers_count > 0);
          break;
      }
    }

    // Aplicar busca por texto
    if (searchTerm) {
      filtered = filtered.filter(repo =>
        repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Aplicar ordenação
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
        break;
      case 'stars':
        filtered.sort((a, b) => b.stargazers_count - a.stargazers_count);
        break;
      case 'forks':
        filtered.sort((a, b) => b.forks_count - a.forks_count);
        break;
      case 'alphabetical':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredRepos(filtered);
  }, [repositories, filterType, searchTerm, sortBy, favoriteRepos, activeFilters]);

  // Função para alternar filtros múltiplos - otimizada com useCallback
  const toggleFilter = useCallback((filter: string) => {
    setActiveFilters(prev => {
      const newFilters = new Set(prev);
      if (newFilters.has(filter)) {
        newFilters.delete(filter);
      } else {
        newFilters.add(filter);
      }
      return newFilters;
    });
  }, []);

  // Gerenciar favoritos - otimizado com useCallback
  const toggleFavorite = useCallback((repoId: number) => {
    setFavoriteRepos(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(repoId)) {
        newFavorites.delete(repoId);
        toast.success('Removido dos favoritos');
      } else {
        newFavorites.add(repoId);
        toast.success('Adicionado aos favoritos');
      }
      return newFavorites;
    });
  }, []);

  // 🎭 DADOS FICTÍCIOS PARA COMMITS
  const generateFakeCommits = (repoName: string, page: number = 1, perPage: number = 10) => {
    const fakeCommits = [
      {
        sha: 'abc123def456',
        commit: {
          message: `feat: implement new dashboard components for ${repoName}`,
          author: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-30T10:30:00Z'
          }
        },
        author: {
          login: 'gabrielcamarate',
          avatar_url: 'https://github.com/gabrielcamarate.png'
        },
        stats: { additions: 45, deletions: 12, total: 57 }
      },
      {
        sha: 'def456ghi789',
        commit: {
          message: 'fix: resolve authentication issues in login flow',
          author: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-29T15:45:00Z'
          }
        },
        author: {
          login: 'gabrielcamarate',
          avatar_url: 'https://github.com/gabrielcamarate.png'
        },
        stats: { additions: 23, deletions: 8, total: 31 }
      },
      {
        sha: 'ghi789jkl012',
        commit: {
          message: 'docs: update README with installation instructions',
          author: {
            name: 'Gabriel Camarate',
            email: '<EMAIL>',
            date: '2024-01-28T09:20:00Z'
          }
        },
        author: {
          login: 'gabrielcamarate',
          avatar_url: 'https://github.com/gabrielcamarate.png'
        },
        stats: { additions: 67, deletions: 3, total: 70 }
      }
    ];

    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedCommits = fakeCommits.slice(startIndex, endIndex);

    return {
      commits: paginatedCommits,
      currentPage: page,
      totalPages: Math.ceil(fakeCommits.length / perPage),
      totalCommits: fakeCommits.length
    };
  };

  // Função para carregar commits com paginação
  const loadCommits = useCallback(async (repo: GitHubRepository, page: number = 1, perPage: number = 10) => {
    setCommitsLoading(true);
    try {
      // 🎭 SIMULAR DELAY
      await new Promise(resolve => setTimeout(resolve, 800));

      const result = generateFakeCommits(repo.name, page, perPage);

      setCommits(result.commits);
      setCommitsPage(result.currentPage);
      setCommitsTotalPages(result.totalPages);
      setCommitsPerPage(perPage);
    } catch (error) {
      console.error('Erro ao carregar commits:', error);
      toast.error('Erro ao carregar commits');
    } finally {
      setCommitsLoading(false);
    }
  }, []);

  // Função para abrir modal de commits
  const openCommitsModal = useCallback((repo: GitHubRepository) => {
    setSelectedRepository(repo);
    setIsCommitsModalOpen(true);
    loadCommits(repo, 1, commitsPerPage);
  }, [loadCommits, commitsPerPage]);

  // Função para carregar commits recentes (3 mais recentes)
  const loadRecentCommits = useCallback(async (repo: GitHubRepository, page: number = 1) => {
    setRecentCommitsLoading(true);
    try {
      // 🎭 SIMULAR DELAY
      await new Promise(resolve => setTimeout(resolve, 600));

      const result = generateFakeCommits(repo.name, page, 3);

      setRecentCommits(result.commits);
      setRecentCommitsPage(result.currentPage);
      setRecentCommitsTotalPages(Math.ceil(result.totalCommits / 3)); // 3 commits por página
    } catch (error) {
      console.error('Erro ao carregar commits recentes:', error);
      toast.error('Erro ao carregar commits recentes');
    } finally {
      setRecentCommitsLoading(false);
    }
  }, []);

  // Função para abrir detalhes do commit
  const openCommitDetails = useCallback((commit: any) => {
    setSelectedCommit(commit);
    setIsCommitDetailsModalOpen(true);
  }, []);

  // 🎭 DADOS FICTÍCIOS PARA BRANCHES
  const generateFakeBranches = (repoName: string) => [
    {
      name: 'main',
      commit: {
        sha: 'abc123def456',
        url: `https://api.github.com/repos/gabrielcamarate/${repoName}/commits/abc123def456`
      },
      protected: true
    },
    {
      name: 'develop',
      commit: {
        sha: 'def456ghi789',
        url: `https://api.github.com/repos/gabrielcamarate/${repoName}/commits/def456ghi789`
      },
      protected: false
    },
    {
      name: 'feature/new-dashboard',
      commit: {
        sha: 'ghi789jkl012',
        url: `https://api.github.com/repos/gabrielcamarate/${repoName}/commits/ghi789jkl012`
      },
      protected: false
    },
    {
      name: 'hotfix/auth-fix',
      commit: {
        sha: 'jkl012mno345',
        url: `https://api.github.com/repos/gabrielcamarate/${repoName}/commits/jkl012mno345`
      },
      protected: false
    }
  ];

  // Função para carregar branches detalhadas
  const loadBranches = useCallback(async (repo: GitHubRepository) => {
    setBranchesLoading(true);
    try {
      // 🎭 SIMULAR DELAY
      await new Promise(resolve => setTimeout(resolve, 500));

      const branchesData = generateFakeBranches(repo.name);
      setBranches(branchesData);
    } catch (error) {
      console.error('Erro ao carregar branches:', error);
      toast.error('Erro ao carregar branches');
    } finally {
      setBranchesLoading(false);
    }
  }, []);

  // Funções de ação para repositórios - otimizada com useCallback
  const handleRepositoryAction = useCallback(async (action: string, repo: GitHubRepository) => {
    switch (action) {
      case 'manage':
        // 🎭 DADOS FICTÍCIOS JÁ ESTÃO COMPLETOS
        setSelectedRepository(repo);
        setIsModalOpen(true);
        break;
      case 'generate-post':
        toast.success(`Gerando post para ${repo.name}...`);
        break;
      case 'view-github':
        window.open(repo.html_url, '_blank');
        break;
      case 'clone':
        navigator.clipboard.writeText(repo.clone_url);
        toast.success('URL do repositório copiada!');
        break;
      case 'analyze':
        toast.success(`Analisando ${repo.name}...`);
        break;
      case 'favorite':
        toggleFavorite(repo.id);
        break;
      case 'share':
        if (navigator.share) {
          navigator.share({
            title: repo.name,
            text: repo.description || `Confira o repositório ${repo.name}`,
            url: repo.html_url
          });
        } else {
          navigator.clipboard.writeText(repo.html_url);
          toast.success('Link copiado para a área de transferência!');
        }
        break;
      default:
        break;
    }
  }, [toggleFavorite]); // 🎭 REMOVIDO loadRepositoryDetails











  return (
    <DashboardLayout>
      {/* Estilos personalizados */}
      <style>{customStyles}</style>
      <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent leading-tight">
              Repositórios GitHub
            </h1>
            <p className="text-slate-300 mt-1 sm:mt-2 text-sm sm:text-base">
              Gerencie e selecione seus repositórios para geração de posts
            </p>
          </div>

          <Button
            onClick={handleRefresh}
            disabled={loading}
            size="sm"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 w-full sm:w-auto"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            <span className="hidden sm:inline">Atualizar</span>
            <span className="sm:hidden">Sync</span>
          </Button>
        </div>

        {/* Loading Progress Indicator */}
        {loadingProgress && (
          <div className="bg-slate-800/50 backdrop-blur-xl border border-slate-700/50 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between text-sm text-slate-300 mb-2">
              <span>Carregando dados dos repositórios...</span>
              <span>{loadingProgress.current} de {loadingProgress.total}</span>
            </div>
            <div className="w-full bg-slate-700/50 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(loadingProgress.current / loadingProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Advanced Search and Filters */}
        <Card className="group relative overflow-hidden bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardContent className="relative p-4 sm:p-6">
            <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
              {/* Search Input */}
              <div className="flex-1 relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                  <Search className="w-5 h-5" />
                </div>
                <Input
                  id="repository-search"
                  name="repository-search"
                  placeholder="Buscar por nome, descrição ou linguagem..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 pr-4 py-3 bg-slate-700/50 border-slate-600/50 text-white placeholder-slate-400 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-base"
                />
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm('')}
                    size="sm"
                    variant="ghost"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white p-1 h-auto"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {/* Sort and Filter Controls */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                <div className="flex items-center space-x-3">
                  <label className="text-slate-300 text-sm font-medium whitespace-nowrap flex items-center">
                    <ArrowUpDown className="w-4 h-4 mr-2 text-slate-400" />
                    Ordenar:
                  </label>
                  <div className="relative">
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="bg-slate-800/80 border border-slate-600/50 rounded-lg px-4 py-2.5 pr-10 text-white text-sm focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 min-w-[160px] appearance-none cursor-pointer hover:bg-slate-700/80"
                    >
                      <option value="recent">📅 Mais Recentes</option>
                      <option value="stars">⭐ Mais Stars</option>
                      <option value="forks">🔀 Mais Forks</option>
                      <option value="alphabetical">🔤 A-Z</option>
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none" />
                  </div>
                </div>


              </div>
            </div>

            {/* Active Filters Summary */}
            {(searchTerm || filterType !== 'all' || activeFilters.size > 0) && (
              <div className="mt-4 pt-4 border-t border-slate-700/50">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-slate-400 text-sm font-medium">Filtros ativos:</span>
                  {searchTerm && (
                    <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30 hover:bg-blue-500/20 transition-colors duration-300">
                      <Search className="w-3 h-3 mr-1" />
                      "{searchTerm}"
                    </Badge>
                  )}
                  {filterType !== 'all' && !activeFilters.has(filterType) && (
                    <Badge className="bg-purple-500/10 text-purple-400 border-purple-500/30 hover:bg-purple-500/20 transition-colors duration-300">
                      Tipo: {filterType}
                    </Badge>
                  )}
                  {Array.from(activeFilters).map(filter => (
                    <Badge
                      key={filter}
                      className="bg-green-500/10 text-green-400 border-green-500/30 hover:bg-green-500/20 transition-colors duration-300 cursor-pointer"
                      onClick={() => toggleFilter(filter)}
                    >
                      <Filter className="w-3 h-3 mr-1" />
                      {filter === 'public' ? 'Públicos' :
                        filter === 'private' ? 'Privados' :
                          filter === 'fork' ? 'Forks' :
                            filter === 'source' ? 'Originais' :
                              filter === 'favorites' ? 'Favoritos' :
                                filter === 'stars' ? 'Com Stars' : filter}
                      <X className="w-3 h-3 ml-1" />
                    </Badge>
                  ))}
                  <Button
                    onClick={() => {
                      requestAnimationFrame(() => {
                        setSearchTerm('');
                        setFilterType('all');
                        setActiveFilters(new Set());
                      });
                    }}
                    size="sm"
                    variant="ghost"
                    className="text-slate-400 hover:text-white text-xs ml-2 px-2 py-1 h-auto"
                  >
                    <X className="w-3 h-3 mr-1" />
                    Limpar
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stats Cards - Interativos como filtros */}
        {!loading && repositories.length > 0 && (
          <div className="grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4">
            {[
              { key: 'all', label: 'Total', count: stats.total, icon: GitBranch, color: 'text-purple-400' },
              { key: 'public', label: 'Públicos', count: stats.public, icon: Globe, color: 'text-green-400' },
              { key: 'private', label: 'Privados', count: stats.private, icon: Lock, color: 'text-yellow-400' },
              { key: 'source', label: 'Originais', count: stats.source, icon: Code, color: 'text-blue-400' },
              { key: 'fork', label: 'Forks', count: stats.forks, icon: GitFork, color: 'text-orange-400' },
              { key: 'favorites', label: 'Favoritos', count: stats.favorites, icon: Heart, color: 'text-red-400' },
              { key: 'stars', label: 'Stars', count: stats.stars, icon: Star, color: 'text-pink-400' },
            ].map(({ key, label, count, icon: Icon, color }) => {
              const isSelected = key === 'all'
                ? (filterType === 'all' && activeFilters.size === 0)
                : activeFilters.has(key);
              const isHovered = false;

              return (
                <Card
                  key={key}
                  className={`
                    relative overflow-hidden transition-all duration-500 transform cursor-pointer backdrop-blur-xl
                    ${isSelected || isHovered
                      ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                      : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
                    }
                  `}
                  onClick={() => {
                    if (key === 'all') {
                      setFilterType('all');
                      setActiveFilters(new Set());
                    } else {
                      toggleFilter(key);
                    }
                  }}

                >
                  {/* Efeito de brilho */}
                  <div className={`
                    absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10
                    transition-opacity duration-500 ${isHovered || isSelected ? 'opacity-100' : 'opacity-0'}
                  `} />

                  <CardContent className="p-3 sm:p-4 relative z-10">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`
                          text-xs sm:text-sm transition-colors duration-300
                          ${isHovered || isSelected ? 'text-blue-300' : 'text-slate-400'}
                        `}>
                          {label}
                        </p>
                        <p className={`
                          text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400
                          bg-clip-text text-transparent transition-all duration-300
                          ${isHovered || isSelected ? 'scale-110' : ''}
                        `}>
                          {count}
                        </p>
                      </div>
                      <div className="relative">
                        <Icon className={`
                          w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300
                          ${isHovered || isSelected ? 'text-blue-400 scale-110' : color}
                        `} />
                        {(isHovered || isSelected) && (
                          <div className="absolute inset-0 w-6 h-6 sm:w-8 sm:h-8 bg-blue-400 rounded-full animate-ping opacity-30" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}



        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-purple-400 mx-auto mb-4" />
              <p className="text-slate-300">Carregando repositórios...</p>
            </div>
          </div>
        )}

        {/* Results Summary */}
        {!loading && repositories.length > 0 && (
          <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
            <div className="flex items-center space-x-4">
              <span>
                Mostrando <span className="text-white font-medium">{filteredRepos.filter(repo => visibleRepos.has(repo.id)).length}</span> de{' '}
                <span className="text-white font-medium">{repositories.length}</span> repositórios
                {loadingProgress && (
                  <span className="text-blue-400 ml-2">
                    (carregando {loadingProgress.current}/{loadingProgress.total})
                  </span>
                )}
              </span>
              {filteredRepos.length !== repositories.length && (
                <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30">
                  Filtrado
                </Badge>
              )}
            </div>
            {filteredRepos.length > 0 && (
              <div className="flex items-center text-slate-500 text-sm">
                <ArrowUpDown className="w-3 h-3 mr-1" />
                Ordenado por: {
                  sortBy === 'recent' ? '📅 Mais Recentes' :
                    sortBy === 'stars' ? '⭐ Mais Stars' :
                      sortBy === 'forks' ? '🔀 Mais Forks' :
                        '🔤 A-Z'
                }
              </div>
            )}
          </div>
        )}

        {/* Loading Message */}
        {!loading && repositories.length > 0 && visibleRepos.size === 0 && (
          <div className="text-center py-12">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
              <div className="text-slate-300">
                Preparando repositórios...
              </div>
              <div className="text-slate-500 text-sm">
                Os repositórios aparecerão conforme são carregados
              </div>
            </div>
          </div>
        )}

        {/* Repositories Grid */}
        {!loading && filteredRepos.length > 0 && visibleRepos.size > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
            {filteredRepos
              .filter(repo => visibleRepos.has(repo.id)) // Mostrar apenas repositórios visíveis
              .map((repo) => (
                <Card
                  key={repo.id}
                  className="group relative overflow-hidden bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl hover:shadow-2xl hover:shadow-blue-500/20 hover:border-blue-500/50 hover:scale-[1.02] transition-all duration-500 cursor-pointer animate-in fade-in slide-in-from-bottom-4 duration-700"
                  onClick={() => handleRepositoryAction('view', repo)}
                >
                  {/* Loading Overlay - Melhorado */}
                  {loadingRepos.has(repo.id) && (
                    <div className="absolute inset-0 bg-slate-900/90 backdrop-blur-md z-20 flex items-center justify-center rounded-lg">
                      <div className="flex flex-col items-center space-y-3 p-4">
                        <div className="relative">
                          <div className="w-10 h-10 border-3 border-blue-400/30 border-t-blue-400 rounded-full animate-spin"></div>
                          <div className="absolute inset-0 w-10 h-10 border-3 border-transparent border-r-purple-400 rounded-full animate-spin animation-delay-150"></div>
                        </div>
                        <div className="text-center">
                          <span className="text-sm text-slate-200 font-medium">Carregando dados...</span>
                          <div className="text-xs text-slate-400 mt-1">Commits e linguagens</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Animated Background Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

                  <CardHeader className="pb-3 relative z-10">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <GitBranch className="w-4 h-4 text-blue-400 flex-shrink-0 group-hover:text-blue-300 group-hover:drop-shadow-[0_0_8px_rgba(59,130,246,0.6)] transition-all duration-300" />
                          <CardTitle className="text-white text-base sm:text-lg truncate group-hover:text-blue-100 transition-colors duration-300">
                            {repo.name}
                          </CardTitle>
                          {repo.private && (
                            <Lock className="w-3 h-3 text-yellow-400 flex-shrink-0 group-hover:text-yellow-300 group-hover:drop-shadow-[0_0_6px_rgba(251,191,36,0.8)] transition-all duration-300" />
                          )}
                          {favoriteRepos.has(repo.id) && (
                            <Heart className="w-3 h-3 text-red-400 fill-current flex-shrink-0 group-hover:text-red-300 group-hover:drop-shadow-[0_0_6px_rgba(248,113,113,0.8)] group-hover:scale-110 transition-all duration-300" />
                          )}
                        </div>
                        <CardDescription className="text-slate-300 text-xs sm:text-sm line-clamp-2">
                          {repo.description || 'Sem descrição disponível'}
                        </CardDescription>
                      </div>

                      {/* Quick Stats Badge */}
                      <div className="flex flex-col items-end space-y-1 ml-2">
                        {repo.stargazers_count > 0 && (
                          <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30 text-xs px-2 py-0.5">
                            <Star className="w-3 h-3 mr-1" />
                            {repo.stargazers_count}
                          </Badge>
                        )}
                        {repo.language && (
                          <Badge className="bg-slate-700/50 text-slate-300 border-slate-600/50 text-xs px-2 py-0.5">
                            <div className={`w-2 h-2 rounded-full mr-1 ${getLanguageColor(repo.language)}`} />
                            {repo.language}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0 relative z-10">
                    {/* Languages */}
                    <div className="mb-3">
                      {loadingRepos.has(repo.id) ? (
                        <div className="flex items-center space-x-2 text-slate-400">
                          <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                          <span className="text-xs">Carregando linguagens...</span>
                        </div>
                      ) : repo.languages && repo.languages.length > 0 ? (
                        <div className="space-y-2">
                          <div className="flex flex-wrap gap-1">
                            {repo.languages.slice(0, 5).map((lang) => (
                              <Badge
                                key={lang.name}
                                variant="outline"
                                className="text-xs text-slate-300 border-slate-500/30 bg-slate-700/30 group-hover:border-slate-400/50 group-hover:bg-slate-600/40 group-hover:text-slate-200 transition-all duration-300"
                              >
                                <div className={`w-2 h-2 rounded-full mr-1 ${getLanguageColor(lang.name)}`} />
                                {lang.name}
                              </Badge>
                            ))}
                            {repo.languages.length > 5 && (
                              <Badge variant="outline" className="text-xs text-slate-400 border-slate-500/30">
                                +{repo.languages.length - 5} mais
                              </Badge>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getLanguageColor(repo.language)}`}></div>
                          <span className="text-slate-300 text-xs sm:text-sm">
                            {repo.language || 'N/A'}
                          </span>
                          {repo.language && (
                            <Badge variant="outline" className="text-xs text-slate-400 border-slate-500/30">
                              Principal
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-5 gap-2 sm:gap-3 mb-4">
                      <div
                        className="text-center cursor-pointer hover:bg-slate-700/30 rounded-lg p-2 transition-all duration-300 group/commits"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (repo.totalCommits && repo.totalCommits > 0) {
                            openCommitsModal(repo);
                          }
                        }}
                        title={repo.totalCommits && repo.totalCommits > 0 ? "Clique para ver commits" : "Nenhum commit disponível"}
                      >
                        <div className="flex items-center justify-center mb-1">
                          <GitBranch className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400 group-hover:text-blue-300 group-hover/commits:text-blue-300 group-hover:drop-shadow-[0_0_6px_rgba(59,130,246,0.6)] transition-all duration-300" />
                        </div>
                        <div className="text-xs sm:text-sm font-medium text-white group-hover/commits:text-blue-300 transition-colors duration-300">
                          {loadingRepos.has(repo.id) || loadingDetails.has(repo.id) ? (
                            <div className="flex items-center justify-center">
                              <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                            </div>
                          ) : (
                            repo.totalCommits ?? '--'
                          )}
                        </div>
                        <div className="text-xs text-slate-400 group-hover/commits:text-blue-400 transition-colors duration-300">Commits</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 group-hover:text-yellow-300 group-hover:drop-shadow-[0_0_6px_rgba(251,191,36,0.8)] group-hover:scale-110 transition-all duration-300" />
                        </div>
                        <div className="text-xs sm:text-sm font-medium text-white">{repo.stargazers_count}</div>
                        <div className="text-xs text-slate-400">Stars</div>
                      </div>

                      <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <GitBranch className="w-3 h-3 sm:w-4 sm:h-4 text-cyan-400 group-hover:text-cyan-300 group-hover:drop-shadow-[0_0_6px_rgba(34,211,238,0.8)] transition-all duration-300" />
                      </div>
                      <div className="text-xs sm:text-sm font-medium text-white">
                        {loadingRepos.has(repo.id) || loadingDetails.has(repo.id) ? (
                          <div className="flex items-center justify-center">
                            <Loader2 className="w-4 h-4 animate-spin text-cyan-400" />
                          </div>
                        ) : (
                          repo.branchesCount ?? '--'
                        )}
                      </div>
                      <div className="text-xs text-slate-400">Branches</div>
                    </div>

                    <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <GitFork className="w-3 h-3 sm:w-4 sm:h-4 text-green-400 group-hover:text-green-300 group-hover:drop-shadow-[0_0_6px_rgba(34,197,94,0.8)] transition-all duration-300" />
                        </div>
                        <div className="text-xs sm:text-sm font-medium text-white">{repo.forks_count}</div>
                        <div className="text-xs text-slate-400">Forks</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-purple-400 group-hover:text-purple-300 group-hover:drop-shadow-[0_0_6px_rgba(147,51,234,0.8)] transition-all duration-300" />
                        </div>
                        <div className="text-xs sm:text-sm font-medium text-white">
                          {formatDate(repo.updated_at)}
                        </div>
                        <div className="text-xs text-slate-400">Atualizado</div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-3 border-t border-slate-600/30">
                      <div className="flex items-center space-x-1">
                        {/* Favorite Button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRepositoryAction('favorite', repo);
                          }}
                          className={`p-2 transition-all duration-300 ${favoriteRepos.has(repo.id)
                            ? 'text-red-400 hover:text-red-300 hover:bg-red-500/10'
                            : 'text-slate-400 hover:text-red-400 hover:bg-red-500/10'
                            }`}
                          title={favoriteRepos.has(repo.id) ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
                        >
                          <Heart className={`w-4 h-4 ${favoriteRepos.has(repo.id) ? 'fill-current' : ''}`} />
                        </Button>

                        {/* Share Button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRepositoryAction('share', repo);
                          }}
                          className="text-slate-400 hover:text-blue-400 hover:bg-blue-500/10 p-2 transition-all duration-300"
                          title="Compartilhar"
                        >
                          <Share2 className="w-4 h-4" />
                        </Button>

                        {/* GitHub Link */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRepositoryAction('view-github', repo);
                          }}
                          className="text-slate-400 hover:text-white hover:bg-slate-500/10 p-2 transition-all duration-300"
                          title="Ver no GitHub"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>

                        {/* Manage Button */}
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRepositoryAction('manage', repo);
                          }}
                          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white text-xs px-4 py-2 shadow-lg hover:shadow-xl hover:shadow-purple-500/50 hover:scale-105 group-hover:animate-pulse transition-all duration-300"
                        >
                          <Settings className="w-3 h-3 mr-1" />
                          Gerenciar
                        </Button>
                      </div>


                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        )}



        {/* Empty State */}
        {!loading && filteredRepos.length === 0 && (
          <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <CardContent className="relative text-center py-12 sm:py-16">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-slate-500/20 rounded-full blur-2xl" />
                <GitBranch className="relative w-16 h-16 sm:w-20 sm:h-20 text-slate-400 mx-auto" />
              </div>

              <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent mb-3">
                {searchTerm || filterType !== 'all'
                  ? 'Nenhum repositório encontrado'
                  : repositories.length === 0
                    ? 'Nenhum repositório disponível'
                    : 'Filtros muito restritivos'
                }
              </h3>

              <p className="text-slate-400 mb-8 text-base sm:text-lg max-w-md mx-auto leading-relaxed">
                {searchTerm || filterType !== 'all'
                  ? 'Tente ajustar os filtros ou termo de busca para encontrar repositórios'
                  : repositories.length === 0
                    ? 'Conecte sua conta GitHub para ver seus repositórios e começar a gerar posts incríveis'
                    : 'Ajuste os filtros para ver mais repositórios'
                }
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4">
                {(searchTerm || filterType !== 'all') ? (
                  <Button
                    onClick={() => {
                      requestAnimationFrame(() => {
                        setSearchTerm('');
                        setFilterType('all');
                      });
                    }}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Limpar Filtros
                  </Button>
                ) : (
                  <Button
                    onClick={handleRefresh}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Sincronizar GitHub
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={() => window.open('https://github.com/new', '_blank')}
                  className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 hover:text-white hover:border-slate-500/70 transition-all duration-300"
                >
                  <GitBranch className="w-4 h-4 mr-2" />
                  Criar Repositório
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Repository Management Modal */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="bg-gradient-to-br from-slate-900/98 via-slate-800/95 to-slate-900/98 backdrop-blur-2xl border border-slate-700/50 shadow-2xl max-h-[90vh] overflow-y-auto p-0">
            {selectedRepository && (
              <div className="relative">
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-blue-600/5 pointer-events-none" />

                <div className="relative p-8 space-y-8">
                  {/* Header Section */}
                  <div className="border-b border-slate-700/50 pb-6">
                    <DialogHeader>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div className="flex items-center space-x-4">
                          <div className="relative">
                            <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl" />
                            <GitBranch className="relative w-10 h-10 text-blue-400" />
                          </div>
                          <div>
                            <DialogTitle className="text-white text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent">
                              {selectedRepository.name}
                            </DialogTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              {selectedRepository.private && (
                                <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30 hover:bg-yellow-500/20">
                                  <Lock className="w-3 h-3 mr-1" />
                                  Private
                                </Badge>
                              )}
                              {selectedRepository.fork && (
                                <Badge className="bg-orange-500/10 text-orange-400 border-orange-500/30 hover:bg-orange-500/20">
                                  <GitFork className="w-3 h-3 mr-1" />
                                  Fork
                                </Badge>
                              )}
                              {selectedRepository.language && (
                                <Badge className="bg-slate-700/50 text-slate-300 border-slate-600/50">
                                  <div className={`w-2 h-2 rounded-full mr-2 ${getLanguageColor(selectedRepository.language)}`} />
                                  {selectedRepository.language}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        <Button
                          onClick={() => window.open(selectedRepository.html_url, '_blank')}
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500/50 focus:outline-none"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Ver no GitHub
                        </Button>
                      </div>

                      <DialogDescription className="text-slate-300 text-base mt-4 leading-relaxed">
                        {selectedRepository.description || 'Este repositório não possui uma descrição.'}
                      </DialogDescription>
                    </DialogHeader>
                  </div>

                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                    {/* Stats Card */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-blue-500/10 transition-all duration-500 min-h-[400px] flex flex-col lg:col-span-2">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center space-x-3 text-xl">
                          <div className="relative">
                            <div className="absolute inset-0 bg-green-500/20 rounded-full blur-lg" />
                            <Activity className="relative w-6 h-6 text-green-400" />
                          </div>
                          <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                            Estatísticas
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="group/stat text-center p-4 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-xl border border-slate-600/30 hover:border-yellow-500/30 transition-all duration-300">
                            <Star className="w-6 h-6 text-yellow-400 mx-auto mb-2 group-hover/stat:scale-110 transition-transform duration-300" />
                            <div className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-300 bg-clip-text text-transparent mb-1">
                              {selectedRepository.stargazers_count}
                            </div>
                            <div className="text-xs text-slate-400">Stars</div>
                          </div>
                          <div className="group/stat text-center p-4 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-xl border border-slate-600/30 hover:border-green-500/30 transition-all duration-300">
                            <GitFork className="w-6 h-6 text-green-400 mx-auto mb-2 group-hover/stat:scale-110 transition-transform duration-300" />
                            <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent mb-1">
                              {selectedRepository.forks_count}
                            </div>
                            <div className="text-xs text-slate-400">Forks</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3 mb-3">
                              <Code className="w-5 h-5 text-blue-400" />
                              <span className="text-slate-300 font-medium">Linguagens</span>
                            </div>
                            {selectedRepository.languages && selectedRepository.languages.length > 0 ? (
                              <div className="space-y-3">
                                {selectedRepository.languages.map((lang) => (
                                  <div key={lang.name} className="space-y-1">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <div className={`w-3 h-3 rounded-full ${getLanguageColor(lang.name)}`} />
                                        <span className="text-white font-medium">{lang.name}</span>
                                      </div>
                                      <span className="text-slate-300 font-semibold">{lang.percentage}%</span>
                                    </div>
                                    {/* Barra de progresso */}
                                    <div className="w-full bg-slate-700/50 rounded-full h-1.5">
                                      <div
                                        className={`h-1.5 rounded-full ${getLanguageColor(lang.name)} opacity-80`}
                                        style={{ width: `${lang.percentage}%` }}
                                      />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full ${getLanguageColor(selectedRepository.language)}`} />
                                <span className="text-white font-semibold">{selectedRepository.language || 'N/A'}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3">
                              <Calendar className="w-5 h-5 text-purple-400" />
                              <span className="text-slate-300 font-medium">Atualizado</span>
                            </div>
                            <span className="text-white font-semibold">{formatDate(selectedRepository.updated_at)}</span>
                          </div>

                          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3">
                              <Users className="w-5 h-5 text-cyan-400" />
                              <span className="text-slate-300 font-medium">Commits</span>
                            </div>
                            <span className="text-white font-semibold">
                              {selectedRepository.totalCommits ?? '--'}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Right Column - Actions and Links */}
                    <div className="flex flex-col gap-4">
                      {/* Actions Card */}
                      <Card className="bg-slate-800/50 border-slate-700/50 shadow-xl flex-1 flex flex-col">
                        <CardHeader>
                          <CardTitle className="text-white flex items-center space-x-3 text-lg">
                            <MessageSquare className="w-6 h-6 text-purple-400" />
                            <span>Ações</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 flex-grow flex flex-col justify-center">
                          <Button
                            onClick={() => handleRepositoryAction('generate-post', selectedRepository)}
                            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 h-12 text-base"
                          >
                            <Code className="w-5 h-5 mr-3" />
                            Gerar Post do LinkedIn
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => handleRepositoryAction('analyze', selectedRepository)}
                            className="w-full border-green-500/50 text-green-400 hover:bg-green-500/10 h-12 text-base"
                          >
                            <Activity className="w-5 h-5 mr-3" />
                            Analisar Atividade
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => handleRepositoryAction('clone', selectedRepository)}
                            className="w-full border-slate-500/50 text-slate-300 hover:bg-slate-500/10 h-12 text-base"
                          >
                            <GitBranch className="w-5 h-5 mr-3" />
                            Copiar URL Clone
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Quick Links Card */}
                      <Card className="bg-slate-800/50 border-slate-700/50 shadow-xl flex-1 flex flex-col">
                        <CardHeader>
                          <CardTitle className="text-white flex items-center space-x-3 text-lg">
                            <ExternalLink className="w-6 h-6 text-blue-400" />
                            <span>Links Rápidos</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 flex-grow flex flex-col justify-center">
                          <Button
                            variant="outline"
                            onClick={() => window.open(selectedRepository.html_url, '_blank')}
                            className="w-full border-blue-500/50 text-blue-400 hover:bg-blue-500/10 h-12 text-base"
                          >
                            <ExternalLink className="w-5 h-5 mr-3" />
                            Ver no GitHub
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/commits`, '_blank')}
                            className="w-full border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 h-12 text-base"
                          >
                            <Clock className="w-5 h-5 mr-3" />
                            Ver Commits
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/issues`, '_blank')}
                            className="w-full border-orange-500/50 text-orange-400 hover:bg-orange-500/10 h-12 text-base"
                          >
                            <AlertCircle className="w-5 h-5 mr-3" />
                            Ver Issues
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Advanced Analytics Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Commit Activity */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-yellow-500/10 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-yellow-600/5 via-transparent to-orange-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center justify-between text-lg">
                          <div className="flex items-center space-x-3">
                            <div className="relative">
                              <div className="absolute inset-0 bg-yellow-500/20 rounded-full blur-lg" />
                              <Clock className="relative w-6 h-6 text-yellow-400" />
                            </div>
                            <span className="bg-gradient-to-r from-yellow-400 to-orange-300 bg-clip-text text-transparent">
                              Atividade de Commits
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {/* Paginação de commits recentes */}
                            {recentCommitsTotalPages > 1 && (
                              <div className="flex items-center space-x-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    if (selectedRepository && recentCommitsPage > 1) {
                                      loadRecentCommits(selectedRepository, recentCommitsPage - 1);
                                    }
                                  }}
                                  disabled={recentCommitsPage <= 1 || recentCommitsLoading}
                                  className="text-yellow-400 hover:bg-yellow-500/10 h-6 w-6 p-0"
                                >
                                  ←
                                </Button>
                                <span className="text-yellow-400 text-xs">
                                  {recentCommitsPage}/{recentCommitsTotalPages}
                                </span>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    if (selectedRepository && recentCommitsPage < recentCommitsTotalPages) {
                                      loadRecentCommits(selectedRepository, recentCommitsPage + 1);
                                    }
                                  }}
                                  disabled={recentCommitsPage >= recentCommitsTotalPages || recentCommitsLoading}
                                  className="text-yellow-400 hover:bg-yellow-500/10 h-6 w-6 p-0"
                                >
                                  →
                                </Button>
                              </div>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openCommitsModal(selectedRepository)}
                              className="border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10"
                            >
                              <ExternalLink className="w-3 h-3 mr-1" />
                              Ver Todos
                            </Button>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-4">
                        {/* Commits reais da GitHub API */}
                        <div className="space-y-3">
                          {recentCommitsLoading ? (
                            <div className="flex items-center justify-center py-4">
                              <Loader2 className="w-6 h-6 animate-spin text-yellow-400" />
                              <span className="ml-2 text-slate-300">Carregando commits...</span>
                            </div>
                          ) : recentCommits.length > 0 ? (
                            recentCommits.map((commit) => (
                              <div
                                key={commit.sha}
                                className="flex items-start space-x-3 p-3 bg-slate-700/30 rounded-lg border border-slate-600/20 hover:border-yellow-500/30 transition-all duration-300 cursor-pointer"
                                onClick={() => openCommitDetails(commit)}
                                title="Clique para ver detalhes do commit"
                              >
                                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <p className="text-white text-sm font-medium truncate">{commit.commit.message}</p>
                                  <div className="flex items-center space-x-2 mt-1">
                                    <span className="text-slate-400 text-xs">{commit.commit.author.name}</span>
                                    <span className="text-slate-500 text-xs">•</span>
                                    <span className="text-slate-400 text-xs">
                                      {new Date(commit.commit.author.date).toLocaleDateString('pt-BR', {
                                        day: '2-digit',
                                        month: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </span>
                                    <span className="text-slate-500 text-xs">•</span>
                                    <code className="text-slate-400 text-xs bg-slate-800/50 px-1 rounded">
                                      {commit.sha.substring(0, 7)}
                                    </code>
                                  </div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-4 text-slate-400">
                              Nenhum commit encontrado
                            </div>
                          )}
                        </div>

                        {recentCommits.length > 0 && (
                          <div className="text-center pt-2">
                            <p className="text-slate-400 text-sm">
                              Mostrando {recentCommits.length} commits mais recentes
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Repository Insights */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center space-x-3 text-lg">
                          <div className="relative">
                            <div className="absolute inset-0 bg-purple-500/20 rounded-full blur-lg" />
                            <TrendingUp className="relative w-6 h-6 text-purple-400" />
                          </div>
                          <span className="bg-gradient-to-r from-purple-400 to-pink-300 bg-clip-text text-transparent">
                            Insights do Repositório
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-4">
                        <div className="grid grid-cols-2 gap-3">
                          <div className="text-center p-3 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg border border-slate-600/30">
                            <Eye className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                            <div className="text-lg font-bold text-white">
                              {selectedRepository.insights?.views !== null && selectedRepository.insights?.views !== undefined
                                ? selectedRepository.insights.views.toLocaleString('pt-BR')
                                : '--'
                              }
                            </div>
                            <div className="text-xs text-slate-400">Views (2 semanas)</div>
                          </div>
                          <div className="text-center p-3 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg border border-slate-600/30">
                            <Download className="w-5 h-5 text-green-400 mx-auto mb-1" />
                            <div className="text-lg font-bold text-white">
                              {selectedRepository.insights?.clones !== null && selectedRepository.insights?.clones !== undefined
                                ? selectedRepository.insights.clones.toLocaleString('pt-BR')
                                : '--'
                              }
                            </div>
                            <div className="text-xs text-slate-400">Clones (2 semanas)</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Tamanho do repositório</span>
                            <span className="text-white font-medium text-sm">
                              {selectedRepository.insights?.size
                                ? `${(selectedRepository.insights.size / 1024).toFixed(1)} MB`
                                : '--'
                              }
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Último push</span>
                            <span className="text-white font-medium text-sm">
                              {selectedRepository.insights?.lastPush
                                ? new Date(selectedRepository.insights.lastPush).toLocaleDateString('pt-BR', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })
                                : formatDate(selectedRepository.updated_at)
                              }
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Branch padrão</span>
                            <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30">
                              {selectedRepository.insights?.defaultBranch || 'main'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Issues abertas</span>
                            <span className="text-white font-medium text-sm">
                              {selectedRepository.insights?.openIssues !== undefined
                                ? selectedRepository.insights.openIssues.toLocaleString('pt-BR')
                                : '--'
                              }
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Watchers</span>
                            <span className="text-white font-medium text-sm">
                              {selectedRepository.insights?.watchers !== undefined
                                ? selectedRepository.insights.watchers.toLocaleString('pt-BR')
                                : '--'
                              }
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Branches Section */}
                  <Card className="group relative overflow-hidden bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <CardHeader className="relative">
                      <CardTitle className="text-white flex items-center justify-between text-lg">
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <div className="absolute inset-0 bg-cyan-500/20 rounded-full blur-lg" />
                            <GitBranch className="relative w-6 h-6 text-cyan-400" />
                          </div>
                          <span className="bg-gradient-to-r from-cyan-400 to-blue-300 bg-clip-text text-transparent">
                            Branches do Repositório
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-cyan-500/10 text-cyan-400 border-cyan-500/30">
                            {selectedRepository.branchesCount || 0} branches
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/branches`, '_blank')}
                            className="border-cyan-500/50 text-cyan-400 hover:bg-cyan-500/10"
                          >
                            <ExternalLink className="w-3 h-3 mr-1" />
                            Ver Todas
                          </Button>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="relative space-y-4">
                      {branchesLoading ? (
                        <div className="flex items-center justify-center py-6">
                          <Loader2 className="w-6 h-6 animate-spin text-cyan-400" />
                          <span className="ml-2 text-slate-300">Carregando branches...</span>
                        </div>
                      ) : branches.length > 0 ? (
                        <div className="space-y-3">
                          {branches.slice(0, 5).map((branch) => (
                            <div
                              key={branch.name}
                              className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg border border-slate-600/20 hover:border-cyan-500/30 transition-all duration-300"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="flex items-center space-x-2">
                                  <GitBranch className="w-4 h-4 text-cyan-400" />
                                  <span className="text-white font-medium">{branch.name}</span>
                                  {branch.name === selectedRepository.insights?.defaultBranch && (
                                    <Badge className="bg-green-500/10 text-green-400 border-green-500/30 text-xs">
                                      Padrão
                                    </Badge>
                                  )}
                                  {branch.protected && (
                                    <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30 text-xs">
                                      Protegida
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <code className="text-xs bg-slate-800/50 text-slate-300 px-2 py-1 rounded">
                                  {branch.commit.sha.substring(0, 7)}
                                </code>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => window.open(`${selectedRepository.html_url}/tree/${branch.name}`, '_blank')}
                                  className="text-cyan-400 hover:text-cyan-300 hover:bg-cyan-500/10 h-6 w-6 p-0"
                                  title="Ver branch no GitHub"
                                >
                                  <ExternalLink className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          ))}

                          {branches.length > 5 && (
                            <div className="text-center pt-2">
                              <p className="text-slate-400 text-sm">
                                Mostrando 5 de {branches.length} branches
                              </p>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-6 text-slate-400">
                          <GitBranch className="w-8 h-8 mx-auto mb-2 text-slate-500" />
                          <p>Nenhuma branch encontrada</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Quick Actions Section */}
                  <Card className="group relative overflow-hidden bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <CardHeader className="relative">
                      <CardTitle className="text-white flex items-center space-x-3 text-lg">
                        <div className="relative">
                          <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-lg" />
                          <Settings className="relative w-6 h-6 text-blue-400" />
                        </div>
                        <span className="bg-gradient-to-r from-blue-400 to-purple-300 bg-clip-text text-transparent">
                          Ações Rápidas para SaaS
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="relative">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Button
                          onClick={() => handleRepositoryAction('generate-post', selectedRepository)}
                          className="h-16 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 flex-col space-y-2 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <MessageSquare className="w-6 h-6" />
                          <span className="text-sm">Gerar Post LinkedIn</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('analyze', selectedRepository)}
                          className="h-16 border-green-500/50 text-green-400 hover:bg-green-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <Activity className="w-6 h-6" />
                          <span className="text-sm">Analisar Atividade</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('clone', selectedRepository)}
                          className="h-16 border-slate-500/50 text-slate-300 hover:bg-slate-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <GitBranch className="w-6 h-6" />
                          <span className="text-sm">Copiar Clone URL</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('share', selectedRepository)}
                          className="h-16 border-blue-500/50 text-blue-400 hover:bg-blue-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <Share2 className="w-6 h-6" />
                          <span className="text-sm">Compartilhar</span>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Modal de Commits */}
        <Dialog open={isCommitsModalOpen} onOpenChange={setIsCommitsModalOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] bg-slate-900/95 backdrop-blur-xl border-slate-700/50">
            <DialogHeader>
              <DialogTitle className="text-white flex items-center">
                <GitBranch className="w-5 h-5 mr-2 text-blue-400" />
                Commits - {selectedRepository?.name}
              </DialogTitle>
              <DialogDescription className="text-slate-300">
                Histórico de commits do repositório
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Controles de Paginação Superior */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <label className="text-slate-300 text-sm">Commits por página:</label>
                  <select
                    value={commitsPerPage}
                    onChange={(e) => {
                      const newPerPage = parseInt(e.target.value);
                      setCommitsPerPage(newPerPage);
                      if (selectedRepository) {
                        loadCommits(selectedRepository, 1, newPerPage);
                      }
                    }}
                    className="bg-slate-800 border border-slate-600 rounded px-3 py-1 text-white text-sm"
                  >
                    <option value={10}>10</option>
                    <option value={30}>30</option>
                    <option value={50}>50</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (selectedRepository && commitsPage > 1) {
                        loadCommits(selectedRepository, commitsPage - 1, commitsPerPage);
                      }
                    }}
                    disabled={commitsPage <= 1 || commitsLoading}
                    className="border-slate-600 text-slate-300"
                  >
                    Anterior
                  </Button>

                  <span className="text-slate-300 text-sm">
                    Página {commitsPage} de {commitsTotalPages}
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (selectedRepository && commitsPage < commitsTotalPages) {
                        loadCommits(selectedRepository, commitsPage + 1, commitsPerPage);
                      }
                    }}
                    disabled={commitsPage >= commitsTotalPages || commitsLoading}
                    className="border-slate-600 text-slate-300"
                  >
                    Próxima
                  </Button>
                </div>
              </div>

              {/* Lista de Commits */}
              <div className="max-h-96 overflow-y-auto space-y-3">
                {commitsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
                    <span className="ml-2 text-slate-300">Carregando commits...</span>
                  </div>
                ) : commits.length > 0 ? (
                  commits.map((commit) => (
                    <div
                      key={commit.sha}
                      className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 hover:bg-slate-700/50 transition-colors duration-300 cursor-pointer"
                      onClick={() => openCommitDetails(commit)}
                      title="Clique para ver detalhes do commit"
                    >
                      <div className="flex items-start space-x-3">
                        {commit.author?.avatar_url && (
                          <img
                            src={commit.author.avatar_url}
                            alt={commit.author.login}
                            className="w-8 h-8 rounded-full"
                          />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-white font-medium">
                              {commit.commit.author.name}
                            </span>
                            <span className="text-slate-400 text-sm">
                              {new Date(commit.commit.author.date).toLocaleDateString('pt-BR')}
                            </span>
                            <code className="text-xs bg-slate-700 text-slate-300 px-2 py-1 rounded">
                              {commit.sha.substring(0, 7)}
                            </code>
                          </div>
                          <p className="text-slate-300 text-sm leading-relaxed">
                            {commit.commit.message}
                          </p>
                          <div className="flex items-center space-x-2 mt-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(commit.html_url, '_blank')}
                              className="text-blue-400 hover:text-blue-300 text-xs"
                            >
                              <ExternalLink className="w-3 h-3 mr-1" />
                              Ver no GitHub
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-slate-400">
                    Nenhum commit encontrado
                  </div>
                )}
              </div>

              {/* Controles de Paginação Inferior */}
              {commits.length > 0 && (
                <div className="flex items-center justify-center space-x-2 pt-4 border-t border-slate-700/50">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (selectedRepository && commitsPage > 1) {
                        loadCommits(selectedRepository, commitsPage - 1, commitsPerPage);
                      }
                    }}
                    disabled={commitsPage <= 1 || commitsLoading}
                    className="border-slate-600 text-slate-300"
                  >
                    ← Anterior
                  </Button>

                  <span className="text-slate-300 text-sm px-4">
                    {commitsPage} / {commitsTotalPages}
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (selectedRepository && commitsPage < commitsTotalPages) {
                        loadCommits(selectedRepository, commitsPage + 1, commitsPerPage);
                      }
                    }}
                    disabled={commitsPage >= commitsTotalPages || commitsLoading}
                    className="border-slate-600 text-slate-300"
                  >
                    Próxima →
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Modal de Detalhes do Commit */}
        <Dialog open={isCommitDetailsModalOpen} onOpenChange={setIsCommitDetailsModalOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] bg-slate-900/95 backdrop-blur-xl border-slate-700/50">
            <DialogHeader>
              <DialogTitle className="text-white flex items-center">
                <GitBranch className="w-5 h-5 mr-2 text-blue-400" />
                Detalhes do Commit
              </DialogTitle>
              <DialogDescription className="text-slate-300">
                Informações completas do commit
              </DialogDescription>
            </DialogHeader>

            {selectedCommit && (
              <div className="space-y-6">
                {/* Header do Commit */}
                <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                  <div className="flex items-start space-x-4">
                    {selectedCommit.author?.avatar_url && (
                      <img
                        src={selectedCommit.author.avatar_url}
                        alt={selectedCommit.author.login}
                        className="w-12 h-12 rounded-full border-2 border-slate-600"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-white font-semibold">
                          {selectedCommit.commit.author.name}
                        </h3>
                        {selectedCommit.author?.login && (
                          <span className="text-slate-400 text-sm">
                            @{selectedCommit.author.login}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-3 text-sm text-slate-400">
                        <span>
                          {new Date(selectedCommit.commit.author.date).toLocaleDateString('pt-BR', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                        <span>•</span>
                        <code className="bg-slate-700 px-2 py-1 rounded text-slate-300">
                          {selectedCommit.sha}
                        </code>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Mensagem do Commit */}
                <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                  <h4 className="text-white font-medium mb-3 flex items-center">
                    <MessageSquare className="w-4 h-4 mr-2 text-blue-400" />
                    Mensagem do Commit
                  </h4>
                  <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-600/30">
                    <pre className="text-slate-300 text-sm whitespace-pre-wrap font-mono leading-relaxed">
                      {selectedCommit.commit.message}
                    </pre>
                  </div>
                </div>

                {/* Informações Técnicas */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                    <h4 className="text-white font-medium mb-3 flex items-center">
                      <Users className="w-4 h-4 mr-2 text-green-400" />
                      Autor
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Nome:</span>
                        <span className="text-white">{selectedCommit.commit.author.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Email:</span>
                        <span className="text-white text-xs">{selectedCommit.commit.author.email}</span>
                      </div>
                      {selectedCommit.author?.login && (
                        <div className="flex justify-between">
                          <span className="text-slate-400">GitHub:</span>
                          <span className="text-white">@{selectedCommit.author.login}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                    <h4 className="text-white font-medium mb-3 flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-yellow-400" />
                      Informações
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">SHA:</span>
                        <code className="text-white bg-slate-700 px-2 py-1 rounded text-xs">
                          {selectedCommit.sha.substring(0, 12)}...
                        </code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Data:</span>
                        <span className="text-white">
                          {new Date(selectedCommit.commit.author.date).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Hora:</span>
                        <span className="text-white">
                          {new Date(selectedCommit.commit.author.date).toLocaleTimeString('pt-BR')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Ações */}
                <div className="flex items-center justify-between pt-4 border-t border-slate-700/50">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => window.open(selectedCommit.html_url, '_blank')}
                      className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Ver no GitHub
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(selectedCommit.sha);
                        toast.success('SHA copiado para a área de transferência!');
                      }}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
                    >
                      <Code className="w-4 h-4 mr-2" />
                      Copiar SHA
                    </Button>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => setIsCommitDetailsModalOpen(false)}
                    className="text-slate-400 hover:text-white"
                  >
                    Fechar
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
