import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configuração de produção para HTTPS
 */
export const productionConfig = {
  // Configurações do servidor
  server: {
    port: process.env.PORT || 3001,
    httpsPort: process.env.HTTPS_PORT || 443,
    httpPort: process.env.HTTP_PORT || 80,
  },

  // Configurações de domínio
  domain: {
    // Domínio principal (substitua pelo seu domínio)
    main: process.env.DOMAIN || 'code2post.com',
    // Subdomínios (opcional)
    www: process.env.WWW_DOMAIN || 'www.code2post.com',
    // Domínios alternativos
    altDomains: process.env.ALT_DOMAINS
      ? process.env.ALT_DOMAINS.split(',')
      : [],
  },

  // Configurações SSL/HTTPS
  ssl: {
    // Let's Encrypt (produção)
    letsEncrypt: {
      enabled: process.env.LETS_ENCRYPT_ENABLED === 'true',
      email: process.env.LETS_ENCRYPT_EMAIL || '<EMAIL>',
      staging: process.env.LETS_ENCRYPT_STAGING === 'true', // true para testes
    },

    // Certificados manuais (fallback)
    manual: {
      enabled: process.env.MANUAL_SSL_ENABLED === 'true',
      certPath:
        process.env.SSL_CERT_PATH ||
        path.join(__dirname, '../../ssl/certificate.pem'),
      keyPath:
        process.env.SSL_KEY_PATH ||
        path.join(__dirname, '../../ssl/private-key.pem'),
      caPath:
        process.env.SSL_CA_PATH ||
        path.join(__dirname, '../../ssl/ca-bundle.pem'),
    },
  },

  // Configurações de segurança
  security: {
    // Headers de segurança
    headers: {
      hsts: {
        maxAge: 31536000, // 1 ano
        includeSubDomains: true,
        preload: true,
      },
      csp: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'https:'],
        fontSrc: ["'self'", 'https:'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },

    // Rate limiting
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutos
      max: 100, // limite por IP
      message: 'Muitas requisições deste IP, tente novamente mais tarde.',
    },
  },

  // Configurações de CORS
  cors: {
    origin: process.env.FRONTEND_URL || 'https://code2post.com',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'],
  },

  // Configurações de logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || path.join(__dirname, '../../logs/app.log'),
    maxSize: '10m',
    maxFiles: '5',
  },

  // Configurações de banco de dados (futuro)
  database: {
    url: process.env.DATABASE_URL,
    ssl: process.env.DATABASE_SSL === 'true',
  },
};

/**
 * Verificar se estamos em produção
 */
export const isProduction = () => {
  return process.env.NODE_ENV === 'production';
};

/**
 * Obter configuração baseada no ambiente
 */
export const getConfig = () => {
  if (isProduction()) {
    return productionConfig;
  }

  // Configuração de desenvolvimento
  return {
    ...productionConfig,
    server: {
      ...productionConfig.server,
      port: process.env.PORT || 3001,
    },
    ssl: {
      ...productionConfig.ssl,
      letsEncrypt: {
        ...productionConfig.ssl.letsEncrypt,
        enabled: false,
      },
      manual: {
        ...productionConfig.ssl.manual,
        enabled: true,
      },
    },
  };
};
